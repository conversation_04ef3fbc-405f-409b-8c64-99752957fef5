-- Complete Supabase Schema for IsotopeAI Migration
-- This file contains all table definitions and RLS policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create missing tables that don't exist yet

-- Questions table for Q&A system
CREATE TABLE IF NOT EXISTS questions (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT,
    subject TEXT,
    topic TEXT,
    difficulty TEXT CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
    question_type TEXT CHECK (question_type IN ('Numerical', 'Theory', 'Conceptual', 'Multi-step')),
    exam_type TEXT CHECK (exam_type IN ('JEE Main', 'JEE Advanced', 'NEET', 'BITSAT')),
    tags TEXT[],
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    answer_count INTEGER DEFAULT 0,
    is_answered BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Answers table for Q&A system
CREATE TABLE IF NOT EXISTS answers (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    question_id TEXT NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    is_accepted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comments table for Q&A system
CREATE TABLE IF NOT EXISTS comments (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    parent_type TEXT NOT NULL CHECK (parent_type IN ('question', 'answer')),
    parent_id TEXT NOT NULL,
    content TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table for group chats
CREATE TABLE IF NOT EXISTS messages (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    group_id TEXT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    sender_id TEXT NOT NULL,
    sender_name TEXT,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    metadata JSONB, -- For image URLs, file info, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Study Sessions table (extract from users JSONB)
CREATE TABLE IF NOT EXISTS study_sessions (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id TEXT NOT NULL,
    subject TEXT,
    duration INTEGER NOT NULL, -- in minutes
    mode TEXT CHECK (mode IN ('pomodoro', 'stopwatch')),
    phase TEXT CHECK (phase IN ('work', 'shortBreak', 'longBreak')),
    completed BOOLEAN DEFAULT FALSE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mock Tests table (extract from users JSONB)
CREATE TABLE IF NOT EXISTS mock_tests (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    test_date DATE NOT NULL,
    subject_marks JSONB NOT NULL, -- Array of {subject, marksObtained, totalMarks, subjectColor}
    total_marks_obtained INTEGER NOT NULL,
    total_marks INTEGER NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat Comments table (for AI chat comments)
CREATE TABLE IF NOT EXISTS chat_comments (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    chat_id TEXT NOT NULL REFERENCES ai_chats(id) ON DELETE CASCADE,
    author_id TEXT NOT NULL,
    author_name TEXT,
    author_username TEXT,
    author_photo_url TEXT,
    content TEXT NOT NULL,
    parent_id TEXT REFERENCES chat_comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to existing tables
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS slug TEXT;
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS preview TEXT;
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'approved' CHECK (status IN ('approved', 'pending', 'rejected'));
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS tags TEXT[];
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS comments_count INTEGER DEFAULT 0;
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT FALSE;
ALTER TABLE ai_chats ADD COLUMN IF NOT EXISTS is_starred BOOLEAN DEFAULT FALSE;

-- Add missing columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS display_name TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS photo_url TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS username TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS study_sessions JSONB DEFAULT '{}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS mock_tests JSONB DEFAULT '{}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS welcome_email_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS member_since TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add missing columns to groups table  
ALTER TABLE groups ADD COLUMN IF NOT EXISTS owner_id TEXT;
ALTER TABLE groups ADD COLUMN IF NOT EXISTS last_message JSONB;
ALTER TABLE groups ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_questions_author_id ON questions(author_id);
CREATE INDEX IF NOT EXISTS idx_questions_subject ON questions(subject);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_answers_question_id ON answers(question_id);
CREATE INDEX IF NOT EXISTS idx_answers_author_id ON answers(author_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent ON comments(parent_type, parent_id);
CREATE INDEX IF NOT EXISTS idx_messages_group_id ON messages(group_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_date ON study_sessions(start_time DESC);
CREATE INDEX IF NOT EXISTS idx_mock_tests_user_id ON mock_tests(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_comments_chat_id ON chat_comments(chat_id);
CREATE INDEX IF NOT EXISTS idx_ai_chats_user_id ON ai_chats(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_chats_public ON ai_chats(is_public, view_count DESC);
CREATE INDEX IF NOT EXISTS idx_ai_chats_slug ON ai_chats(slug);

-- Row Level Security (RLS) Policies
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mock_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE exams ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Questions (public read, authenticated write)
CREATE POLICY "Questions are viewable by everyone" ON questions FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert questions" ON questions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own questions" ON questions FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own questions" ON questions FOR DELETE USING (auth.uid()::text = author_id);

-- RLS Policies for Answers (public read, authenticated write)
CREATE POLICY "Answers are viewable by everyone" ON answers FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert answers" ON answers FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own answers" ON answers FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own answers" ON answers FOR DELETE USING (auth.uid()::text = author_id);

-- RLS Policies for Comments (public read, authenticated write)
CREATE POLICY "Comments are viewable by everyone" ON comments FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert comments" ON comments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own comments" ON comments FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own comments" ON comments FOR DELETE USING (auth.uid()::text = author_id);

-- RLS Policies for Messages (group members only)
CREATE POLICY "Messages viewable by group members" ON messages FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM groups
        WHERE groups.id = messages.group_id
        AND auth.uid()::text = ANY(groups.members)
    )
);
CREATE POLICY "Group members can insert messages" ON messages FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM groups
        WHERE groups.id = group_id
        AND auth.uid()::text = ANY(groups.members)
    )
);
CREATE POLICY "Users can update their own messages" ON messages FOR UPDATE USING (auth.uid()::text = sender_id);
CREATE POLICY "Users can delete their own messages" ON messages FOR DELETE USING (auth.uid()::text = sender_id);

-- RLS Policies for Study Sessions (user's own data only)
CREATE POLICY "Users can view their own study sessions" ON study_sessions FOR SELECT USING (auth.uid()::text = user_id);
CREATE POLICY "Users can insert their own study sessions" ON study_sessions FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can update their own study sessions" ON study_sessions FOR UPDATE USING (auth.uid()::text = user_id);
CREATE POLICY "Users can delete their own study sessions" ON study_sessions FOR DELETE USING (auth.uid()::text = user_id);

-- RLS Policies for Mock Tests (user's own data only)
CREATE POLICY "Users can view their own mock tests" ON mock_tests FOR SELECT USING (auth.uid()::text = user_id);
CREATE POLICY "Users can insert their own mock tests" ON mock_tests FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can update their own mock tests" ON mock_tests FOR UPDATE USING (auth.uid()::text = user_id);
CREATE POLICY "Users can delete their own mock tests" ON mock_tests FOR DELETE USING (auth.uid()::text = user_id);

-- RLS Policies for Chat Comments (public read for public chats, authenticated write)
CREATE POLICY "Chat comments viewable for public chats" ON chat_comments FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM ai_chats
        WHERE ai_chats.id = chat_comments.chat_id
        AND (ai_chats.is_public = true OR ai_chats.user_id = auth.uid()::text)
    )
);
CREATE POLICY "Authenticated users can insert chat comments" ON chat_comments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own chat comments" ON chat_comments FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own chat comments" ON chat_comments FOR DELETE USING (auth.uid()::text = author_id);

-- RLS Policies for AI Chats (public read for public chats, user's own data)
CREATE POLICY "Public AI chats are viewable by everyone" ON ai_chats FOR SELECT USING (is_public = true OR auth.uid()::text = user_id);
CREATE POLICY "Users can insert their own AI chats" ON ai_chats FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can update their own AI chats" ON ai_chats FOR UPDATE USING (auth.uid()::text = user_id);
CREATE POLICY "Users can delete their own AI chats" ON ai_chats FOR DELETE USING (auth.uid()::text = user_id);

-- RLS Policies for Users (public read for basic info, user's own data for sensitive info)
CREATE POLICY "User profiles are viewable by everyone" ON users FOR SELECT USING (true);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid()::text = id);

-- RLS Policies for Groups (members only)
CREATE POLICY "Groups viewable by members" ON groups FOR SELECT USING (auth.uid()::text = ANY(members) OR auth.uid()::text = created_by);
CREATE POLICY "Authenticated users can create groups" ON groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Group creators and members can update groups" ON groups FOR UPDATE USING (auth.uid()::text = created_by OR auth.uid()::text = ANY(members));
CREATE POLICY "Group creators can delete groups" ON groups FOR DELETE USING (auth.uid()::text = created_by);

-- RLS Policies for Todos (user's own data and group members)
CREATE POLICY "Users can view their own todos and group todos" ON todos FOR SELECT USING (
    auth.uid()::text = created_by OR
    auth.uid()::text = assigned_to OR
    (group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM groups
        WHERE groups.id = todos.group_id
        AND auth.uid()::text = ANY(groups.members)
    ))
);
CREATE POLICY "Users can insert todos" ON todos FOR INSERT WITH CHECK (auth.uid()::text = created_by);
CREATE POLICY "Users can update their own todos and assigned todos" ON todos FOR UPDATE USING (
    auth.uid()::text = created_by OR auth.uid()::text = assigned_to
);
CREATE POLICY "Users can delete their own todos" ON todos FOR DELETE USING (auth.uid()::text = created_by);

-- RLS Policies for User Subjects (user's own data only)
CREATE POLICY "Users can view their own subjects" ON user_subjects FOR SELECT USING (auth.uid()::text = user_id);
CREATE POLICY "Users can insert their own subjects" ON user_subjects FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can update their own subjects" ON user_subjects FOR UPDATE USING (auth.uid()::text = user_id);
CREATE POLICY "Users can delete their own subjects" ON user_subjects FOR DELETE USING (auth.uid()::text = user_id);

-- RLS Policies for Exams (user's own data only)
CREATE POLICY "Users can view their own exams" ON exams FOR SELECT USING (auth.uid()::text = user_id);
CREATE POLICY "Users can insert their own exams" ON exams FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can update their own exams" ON exams FOR UPDATE USING (auth.uid()::text = user_id);
CREATE POLICY "Users can delete their own exams" ON exams FOR DELETE USING (auth.uid()::text = user_id);
