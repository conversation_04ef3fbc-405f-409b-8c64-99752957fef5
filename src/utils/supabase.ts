import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';
import { User } from '@supabase/supabase-js';

// Type definitions
type Tables = Database['public']['Tables'];
type UserRow = Tables['users']['Row'];
type UserInsert = Tables['users']['Insert'];
type UserUpdate = Tables['users']['Update'];
type StudySessionRow = Tables['study_sessions']['Row'];
type StudySessionInsert = Tables['study_sessions']['Insert'];
type TodoRow = Tables['todos']['Row'];
type TodoInsert = Tables['todos']['Insert'];
type TodoUpdate = Tables['todos']['Update'];
type UserSubjectRow = Tables['userSubjects']['Row'];
type UserSubjectInsert = Tables['userSubjects']['Insert'];
type GroupRow = Tables['groups']['Row'];
type GroupInsert = Tables['groups']['Insert'];
type ExamRow = Tables['exams']['Row'];
type ExamInsert = Tables['exams']['Insert'];
type MockTestRow = Tables['mock_tests']['Row'];
type MockTestInsert = Tables['mock_tests']['Insert'];

// Authentication functions
export const signInWithGoogle = async () => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) throw error;
    return {
      user: null,
      session: null,
      provider: data.provider,
      url: data.url
    };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

export const signInWithEmailPassword = async (email: string, password: string) => {
  try {
    const { data: { user, session }, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return { user, session };
  } catch (error) {
    console.error('Error signing in with email/password:', error);
    throw error;
  }
};

export const signUpWithEmailPassword = async (email: string, password: string) => {
  try {
    const { data: { user, session }, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;
    return { user, session };
  } catch (error) {
    console.error('Error signing up with email/password:', error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

export const getCurrentUser = () => {
  return supabase.auth.getUser();
};

export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return supabase.auth.onAuthStateChange((_event, session) => {
    callback(session?.user ?? null);
  });
};



// User profile functions
export const getUserProfile = async (userId: string): Promise<UserRow | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

export const createUserProfile = async (user: User): Promise<UserRow> => {
  try {
    const userProfile: UserInsert = {
      id: user.id,
      email: user.email!,
      username: user.user_metadata?.username || user.email?.split('@')[0] || `user_${user.id.substring(0, 5)}`,
      display_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
      photo_url: user.user_metadata?.avatar_url || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
      member_since: new Date().toISOString(),
      welcome_email_sent: false,
      daily_target: 120, // 2 hours in minutes
      daily_motivation: 'Stay focused and achieve your goals!',
      day_start_time: 4, // 4 AM
      stats: {},
      progress: {}
    };

    const { data, error } = await supabase
      .from('users')
      .insert(userProfile)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
};

export const updateUserProfile = async (userId: string, updates: UserUpdate): Promise<UserRow> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Study sessions functions
export const saveStudySession = async (sessionData: StudySessionInsert): Promise<StudySessionRow> => {
  try {
    const { data, error } = await supabase
      .from('study_sessions')
      .insert(sessionData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving study session:', error);
    throw error;
  }
};

export const getStudySessions = async (userId: string, startDate?: string, endDate?: string): Promise<StudySessionRow[]> => {
  try {
    let query = supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('start_time', { ascending: false });

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting study sessions:', error);
    return [];
  }
};

export const updateStudySession = async (sessionId: string, updates: Partial<StudySessionRow>): Promise<StudySessionRow> => {
  try {
    const { data, error } = await supabase
      .from('study_sessions')
      .update(updates)
      .eq('id', sessionId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating study session:', error);
    throw error;
  }
};

export const deleteStudySession = async (sessionId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('study_sessions')
      .delete()
      .eq('id', sessionId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting study session:', error);
    throw error;
  }
};

// User subjects functions
export const getUserSubjects = async (userId: string): Promise<UserSubjectRow[]> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .select('*')
      .eq('userId', userId)
      .order('createdAt', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting user subjects:', error);
    return [];
  }
};

export const createUserSubject = async (subjectData: UserSubjectInsert): Promise<UserSubjectRow> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .insert(subjectData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating user subject:', error);
    throw error;
  }
};

export const updateUserSubject = async (subjectId: string, updates: Partial<UserSubjectRow>): Promise<UserSubjectRow> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .update(updates)
      .eq('id', subjectId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user subject:', error);
    throw error;
  }
};

export const deleteUserSubject = async (subjectId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('userSubjects')
      .delete()
      .eq('id', subjectId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting user subject:', error);
    throw error;
  }
};

// Real-time subscriptions
export const subscribeToUserData = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-data-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'users',
        filter: `id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToStudySessions = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('study-sessions-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'study_sessions',
        filter: `user_id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToUserSubjects = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-subjects-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'userSubjects',
        filter: `userId=eq.${userId}`
      },
      callback
    )
    .subscribe();
};

// Todo functions
export const getTodos = async (userId: string): Promise<TodoRow[]> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId)
      .order('createdAt', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting todos:', error);
    return [];
  }
};

export const createTodo = async (todoData: TodoInsert): Promise<TodoRow> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .insert(todoData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating todo:', error);
    throw error;
  }
};

export const updateTodo = async (todoId: string, updates: TodoUpdate): Promise<TodoRow> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .update({
        ...updates,
        updatedAt: Date.now(),
      })
      .eq('id', todoId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating todo:', error);
    throw error;
  }
};

export const deleteTodo = async (todoId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting todo:', error);
    throw error;
  }
};

export const subscribeToTodos = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('todos-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `createdBy=eq.${userId}`
      },
      callback
    )
    .subscribe();
};

// Groups functions
export const getGroups = async (userId: string): Promise<GroupRow[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .or(`createdBy.eq.${userId},owner_id.eq.${userId},members.cs.{${userId}}`)
      .order('last_activity', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting groups:', error);
    return [];
  }
};

export const createGroup = async (groupData: GroupInsert): Promise<GroupRow> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .insert(groupData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating group:', error);
    throw error;
  }
};

export const updateGroup = async (groupId: string, updates: Partial<GroupRow>): Promise<GroupRow> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating group:', error);
    throw error;
  }
};

export const deleteGroup = async (groupId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting group:', error);
    throw error;
  }
};

// Exams functions
export const getExams = async (userId: string): Promise<ExamRow[]> => {
  try {
    const { data, error } = await supabase
      .from('exams')
      .select('*')
      .eq('userId', userId)
      .order('date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting exams:', error);
    return [];
  }
};

export const createExam = async (examData: ExamInsert): Promise<ExamRow> => {
  try {
    const { data, error } = await supabase
      .from('exams')
      .insert(examData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating exam:', error);
    throw error;
  }
};

// Mock tests functions
export const getMockTests = async (userId: string): Promise<MockTestRow[]> => {
  try {
    const { data, error } = await supabase
      .from('mock_tests')
      .select('*')
      .eq('user_id', userId)
      .order('test_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting mock tests:', error);
    return [];
  }
};

export const createMockTest = async (mockTestData: MockTestInsert): Promise<MockTestRow> => {
  try {
    const { data, error } = await supabase
      .from('mock_tests')
      .insert(mockTestData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating mock test:', error);
    throw error;
  }
};
