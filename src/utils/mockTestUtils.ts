// TODO: Implement Supabase mock test utils
import { MockTest, MockTestAnalytics, SubjectMarks } from '../types/mockTest';

// Save a new mock test
export const saveMockTest = async (mockTest: MockTest): Promise<string> => {
  try {
    const userRef = doc(db, "users", mockTest.userId);
    const mockTestId = mockTest.id || `mock-${Date.now()}`;
    
    // Set the id if not provided
    if (!mockTest.id) {
      mockTest.id = mockTestId;
    }
    
    // Add mock test to user's mockTests object
    await setDoc(userRef, {
      mockTests: {
        [mockTestId]: mockTest
      },
      lastUpdated: new Date().toISOString()
    }, { merge: true });
    
    return mockTestId;
  } catch (error) {
    console.error("Error saving mock test:", error);
    throw error;
  }
};

// Get all mock tests for a user
export const getUserMockTests = async (userId: string): Promise<MockTest[]> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return [];
    }
    
    const userData = userDoc.data();
    const mockTests = userData.mockTests || {};
    
    return Object.values(mockTests) as MockTest[];
  } catch (error) {
    console.error("Error getting user mock tests:", error);
    throw error;
  }
};

// Update a mock test
export const updateMockTest = async (userId: string, mockTest: MockTest): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    
    await updateDoc(userRef, {
      [`mockTests.${mockTest.id}`]: mockTest,
      lastUpdated: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    console.error("Error updating mock test:", error);
    throw error;
  }
};

// Delete a mock test
export const deleteMockTest = async (userId: string, mockTestId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return false;
    }
    
    const userData = userDoc.data();
    const mockTests = userData.mockTests || {};
    
    // Remove the mock test
    delete mockTests[mockTestId];
    
    // Update the user document
    await updateDoc(userRef, {
      mockTests: mockTests,
      lastUpdated: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    console.error("Error deleting mock test:", error);
    throw error;
  }
};

// Calculate the total marks and percentage for a test
export const calculateTestTotals = (subjectMarks: SubjectMarks[]): { totalMarksObtained: number, totalMarks: number, percentage: number } => {
  const totalMarksObtained = subjectMarks.reduce((sum, subject) => sum + subject.marksObtained, 0);
  const totalMarks = subjectMarks.reduce((sum, subject) => sum + subject.totalMarks, 0);
  const percentage = totalMarks > 0 ? (totalMarksObtained / totalMarks) * 100 : 0;
  
  return {
    totalMarksObtained,
    totalMarks,
    percentage
  };
};

// Generate analytics from mock tests
export const generateMockTestAnalytics = (mockTests: MockTest[]): MockTestAnalytics => {
  if (!mockTests || mockTests.length === 0) {
    return {
      totalTests: 0,
      averageScore: 0,
      highestScore: {
        testId: '',
        testName: '',
        score: 0,
        percentage: 0
      },
      lowestScore: {
        testId: '',
        testName: '',
        score: 0,
        percentage: 0
      },
      subjectPerformance: {},
      recentTests: []
    };
  }
  
  // Sort tests by date (newest first)
  const sortedTests = [...mockTests].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  
  // Calculate total and average scores across all tests
  const totalMarksObtained = mockTests.reduce((sum, test) => sum + test.totalMarksObtained, 0);
  const totalPossibleMarks = mockTests.reduce((sum, test) => sum + test.totalMarks, 0);
  const averageScore = totalPossibleMarks > 0 ? (totalMarksObtained / totalPossibleMarks) * 100 : 0;
  
  // Find highest and lowest scores
  let highestScoreTest = mockTests[0];
  let lowestScoreTest = mockTests[0];
  
  mockTests.forEach(test => {
    const testPercentage = test.totalMarks > 0 ? (test.totalMarksObtained / test.totalMarks) * 100 : 0;
    const highestPercentage = highestScoreTest.totalMarks > 0 ? 
      (highestScoreTest.totalMarksObtained / highestScoreTest.totalMarks) * 100 : 0;
    const lowestPercentage = lowestScoreTest.totalMarks > 0 ? 
      (lowestScoreTest.totalMarksObtained / lowestScoreTest.totalMarks) * 100 : 0;
    
    if (testPercentage > highestPercentage) {
      highestScoreTest = test;
    }
    
    if (testPercentage < lowestPercentage) {
      lowestScoreTest = test;
    }
  });
  
  // Calculate subject-wise performance
  const subjectPerformance: { [subject: string]: { totalTests: number, averageScore: number, averagePercentage: number } } = {};
  
  mockTests.forEach(test => {
    test.subjectMarks.forEach(subjectMark => {
      if (!subjectPerformance[subjectMark.subject]) {
        subjectPerformance[subjectMark.subject] = {
          totalTests: 0,
          averageScore: 0,
          averagePercentage: 0
        };
      }
      
      subjectPerformance[subjectMark.subject].totalTests += 1;
      subjectPerformance[subjectMark.subject].averageScore += subjectMark.marksObtained;
      subjectPerformance[subjectMark.subject].averagePercentage += 
        (subjectMark.marksObtained / subjectMark.totalMarks) * 100;
    });
  });
  
  // Calculate average for each subject
  Object.keys(subjectPerformance).forEach(subject => {
    const subjectData = subjectPerformance[subject];
    subjectData.averageScore = subjectData.averageScore / subjectData.totalTests;
    subjectData.averagePercentage = subjectData.averagePercentage / subjectData.totalTests;
  });
  
  // Get recent 5 tests
  const recentTests = sortedTests.slice(0, 5);
  
  // Calculate percentages for highest and lowest scores
  const highestPercentage = highestScoreTest.totalMarks > 0 ? 
    (highestScoreTest.totalMarksObtained / highestScoreTest.totalMarks) * 100 : 0;
  const lowestPercentage = lowestScoreTest.totalMarks > 0 ? 
    (lowestScoreTest.totalMarksObtained / lowestScoreTest.totalMarks) * 100 : 0;
  
  return {
    totalTests: mockTests.length,
    averageScore: averageScore,
    highestScore: {
      testId: highestScoreTest.id,
      testName: highestScoreTest.name,
      score: highestScoreTest.totalMarksObtained,
      percentage: highestPercentage
    },
    lowestScore: {
      testId: lowestScoreTest.id,
      testName: lowestScoreTest.name,
      score: lowestScoreTest.totalMarksObtained,
      percentage: lowestPercentage
    },
    subjectPerformance,
    recentTests
  };
}; 