import { create } from 'zustand'

export interface Message {
  id: string
  content: string
  senderId: string
  timestamp: number
  groupId: string
}

export interface ChatGroup {
  id: string
  name: string
  members: string[]
  createdAt: number
  ownerId: string
  inviteCode: string
  isPublic?: boolean
  description?: string
  lastMessage?: Message
}

interface ChatState {
  activeGroupId: string | null
  groups: ChatGroup[]
  messages: { [groupId: string]: Message[] }
  setActiveGroupId: (groupId: string) => void
  addGroup: (group: ChatGroup) => void
  removeGroup: (groupId: string) => void
  addMessage: (message: Message) => void
  setGroups: (groups: ChatGroup[]) => void
  setMessages: (groupId: string, messages: Message[]) => void
}

export const useChatStore = create<ChatState>((set) => ({
  activeGroupId: null,
  groups: [],
  messages: {},
  setActiveGroupId: (groupId) => set({ activeGroupId: groupId }),
  addGroup: (group) =>
    set((state) => ({ groups: [...state.groups, group] })),
  removeGroup: (groupId) =>
    set((state) => ({
      groups: state.groups.filter((g) => g.id !== groupId),
      messages: Object.fromEntries(
        Object.entries(state.messages).filter(([key]) => key !== groupId)
      ),
    })),
  addMessage: (message) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [message.groupId]: [
          ...(state.messages[message.groupId] || []),
          message,
        ],
      },
    })),
  setGroups: (groups) => set({ groups }),
  setMessages: (groupId, messages) =>
    set((state) => ({
      messages: { ...state.messages, [groupId]: messages },
    })),
})) 