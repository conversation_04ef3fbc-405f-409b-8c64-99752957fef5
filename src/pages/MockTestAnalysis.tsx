import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useToast } from "@/components/ui/use-toast";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, FileText, BarChart3, Search, Filter, SortAsc } from "lucide-react";
import { MockTest, MockTestAnalytics } from "@/types/mockTest";
import {
  getUserMockTests,
  deleteMockTest,
  generateMockTestAnalytics,
} from "@/utils/mockTestUtils";
import { AddMockTestButton } from "@/components/mocktest/AddMockTestButton";
import { MockTestCard } from "@/components/mocktest/MockTestCard";
import { EditMockTestDialog } from "@/components/mocktest/EditMockTestDialog";
import { MockTestAnalyticsCards } from "@/components/mocktest/MockTestAnalyticsCards";
import { MockTestCharts } from "@/components/mocktest/MockTestCharts";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Header } from "@/components/shared";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export default function MockTestAnalysis() {
  useDocumentTitle("Mock Test Analysis - IsotopeAI");
  const { user } = useSupabaseAuth();
  const { toast } = useToast();

  const [mockTests, setMockTests] = useState<MockTest[]>([]);
  const [filteredTests, setFilteredTests] = useState<MockTest[]>([]);
  const [mockTestAnalytics, setMockTestAnalytics] = useState<MockTestAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [sortOption, setSortOption] = useState<string>("date-desc");

  const [editingTest, setEditingTest] = useState<MockTest | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingTestId, setDeletingTestId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Get all available subjects from mock tests
  const availableSubjects = [...new Set(mockTests.flatMap((test) => test.subjectMarks.map((sm) => sm.subject)))];

  // Load mock tests and analytics
  useEffect(() => {
    const loadMockTests = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        const tests = await getUserMockTests(user.uid);
        setMockTests(tests);
        setFilteredTests(tests);

        const analytics = generateMockTestAnalytics(tests);
        setMockTestAnalytics(analytics);
      } catch (error) {
        console.error("Error loading mock tests:", error);
        toast({
          title: "Error",
          description: "Failed to load mock tests",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMockTests();
  }, [user, toast]);

  // Filter and sort tests based on search query, subject and sort option
  useEffect(() => {
    if (!mockTests.length) {
      setFilteredTests([]);
      return;
    }

    let filtered = [...mockTests];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter((test) =>
        test.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        test.notes?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by subject
    if (selectedSubject !== "all") {
      filtered = filtered.filter((test) =>
        test.subjectMarks.some((sm) => sm.subject === selectedSubject)
      );
    }

    // Sort tests
    switch (sortOption) {
      case "date-asc":
        filtered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        break;
      case "date-desc":
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        break;
      case "score-asc":
        filtered.sort((a, b) =>
          (a.totalMarksObtained / a.totalMarks) - (b.totalMarksObtained / b.totalMarks)
        );
        break;
      case "score-desc":
        filtered.sort((a, b) =>
          (b.totalMarksObtained / b.totalMarks) - (a.totalMarksObtained / a.totalMarks)
        );
        break;
      default:
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    }

    setFilteredTests(filtered);
  }, [mockTests, searchQuery, selectedSubject, sortOption]);

  // Handle deleting a mock test
  const handleDeleteTest = async () => {
    if (!user || !deletingTestId) return;

    try {
      const success = await deleteMockTest(user.uid, deletingTestId);

      if (success) {
        const updatedTests = mockTests.filter((test) => test.id !== deletingTestId);
        setMockTests(updatedTests);

        const analytics = generateMockTestAnalytics(updatedTests);
        setMockTestAnalytics(analytics);

        toast({
          title: "Success",
          description: "Mock test deleted successfully",
        });
      }
    } catch (error) {
      console.error("Error deleting mock test:", error);
      toast({
        title: "Error",
        description: "Failed to delete mock test",
        variant: "destructive",
      });
    } finally {
      setDeletingTestId(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle refreshing mock tests
  const handleRefreshTests = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const tests = await getUserMockTests(user.uid);
      setMockTests(tests);

      const analytics = generateMockTestAnalytics(tests);
      setMockTestAnalytics(analytics);

      toast({
        title: "Success",
        description: "Mock tests refreshed successfully",
      });
    } catch (error) {
      console.error("Error refreshing mock tests:", error);
      toast({
        title: "Error",
        description: "Failed to refresh mock tests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Authentication Error</AlertTitle>
          <AlertDescription>
            You need to be logged in to view this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <>
      <Header />
      <div className="relative min-h-screen bg-gradient-to-b from-background to-background/80 pt-20 pb-12">

        <div className="container mx-auto py-8 px-4 sm:px-6">
          {/* Header Section with Gradient Background */}
          <motion.div
            className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary/10 via-primary/5 to-background p-6 mb-8 border border-primary/10 shadow-sm"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="relative">
                <h1 className="text-4xl font-bold tracking-tight gradient-heading mb-2">Mock Test Analysis</h1>
                <p className="text-muted-foreground text-lg max-w-2xl">
                  Track and analyze your performance in mock tests to improve your results
                </p>
                <div className="absolute -bottom-1 left-0 h-1 w-24 bg-gradient-to-r from-primary to-primary/40 rounded-full"></div>
              </div>
              <div className="relative">
                <AddMockTestButton
                  onAddMockTest={handleRefreshTests}
                  className="bg-primary hover:bg-primary/90 text-white shadow-md"
                />
              </div>
            </div>
          </motion.div>

        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Tabs defaultValue="analytics" className="w-full">
              <TabsList className="mb-6 w-full sm:w-auto p-1 bg-muted/80 backdrop-blur-sm rounded-xl">
                <TabsTrigger
                  value="analytics"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <BarChart3 className="h-4 w-4" />
                  Analytics
                </TabsTrigger>
                <TabsTrigger
                  value="tests"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <FileText className="h-4 w-4" />
                  My Tests
                </TabsTrigger>
              </TabsList>

          <TabsContent value="analytics">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center h-80 gap-4">
                <div className="relative w-16 h-16">
                  <div className="absolute inset-0 rounded-full border-t-2 border-primary animate-spin"></div>
                  <div className="absolute inset-2 rounded-full border-t-2 border-primary/70 animate-spin" style={{ animationDuration: '1.5s' }}></div>
                  <div className="absolute inset-4 rounded-full border-t-2 border-primary/40 animate-spin" style={{ animationDuration: '2s' }}></div>
                </div>
                <p className="text-muted-foreground animate-pulse">Loading your analytics...</p>
              </div>
            ) : mockTestAnalytics && mockTests.length > 0 ? (
              <motion.div
                className="space-y-8"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div variants={itemVariants}>
                  <MockTestAnalyticsCards analytics={mockTestAnalytics} />
                </motion.div>
                <motion.div variants={itemVariants} className="mb-8">
                  <h2 className="text-2xl font-bold mb-6 gradient-heading">Detailed Performance Analysis</h2>
                  <MockTestCharts analytics={mockTestAnalytics} mockTests={mockTests} />
                </motion.div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <Alert className="bg-card border border-primary/10 shadow-md">
                  <AlertCircle className="h-5 w-5 text-primary" />
                  <AlertTitle className="text-lg font-semibold">No mock tests found</AlertTitle>
                  <AlertDescription className="text-muted-foreground">
                    Add your first mock test to start tracking your performance and see detailed analytics.
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}
          </TabsContent>

          <TabsContent value="tests">
            <motion.div
              className="space-y-6"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <motion.div
                variants={itemVariants}
                className="bg-card/50 backdrop-blur-sm rounded-xl p-6 border border-primary/10 shadow-sm"
              >
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Filter className="h-5 w-5 text-primary" />
                  Filter & Search
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="search" className="text-sm font-medium flex items-center gap-1.5">
                      <Search className="h-3.5 w-3.5 text-muted-foreground" />
                      Search Tests
                    </Label>
                    <div className="relative">
                      <Input
                        id="search"
                        placeholder="Search by name or notes..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-9 bg-background/50 border-primary/20 focus:border-primary transition-all duration-300"
                      />
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-sm font-medium flex items-center gap-1.5">
                      <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                      Filter by Subject
                    </Label>
                    <Select
                      value={selectedSubject}
                      onValueChange={setSelectedSubject}
                    >
                      <SelectTrigger
                        id="subject"
                        className="bg-background/50 border-primary/20 focus:border-primary transition-all duration-300"
                      >
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Subjects</SelectItem>
                        {availableSubjects.map((subject) => (
                          <SelectItem key={subject} value={subject}>
                            {subject}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sort" className="text-sm font-medium flex items-center gap-1.5">
                      <SortAsc className="h-3.5 w-3.5 text-muted-foreground" />
                      Sort by
                    </Label>
                    <Select value={sortOption} onValueChange={setSortOption}>
                      <SelectTrigger
                        id="sort"
                        className="bg-background/50 border-primary/20 focus:border-primary transition-all duration-300"
                      >
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date-desc">Date (Newest first)</SelectItem>
                        <SelectItem value="date-asc">Date (Oldest first)</SelectItem>
                        <SelectItem value="score-desc">Score (Highest first)</SelectItem>
                        <SelectItem value="score-asc">Score (Lowest first)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Separator className="my-6 opacity-50" />

              {isLoading ? (
                <div className="flex flex-col items-center justify-center h-80 gap-4">
                  <div className="relative w-16 h-16">
                    <div className="absolute inset-0 rounded-full border-t-2 border-primary animate-spin"></div>
                    <div className="absolute inset-2 rounded-full border-t-2 border-primary/70 animate-spin" style={{ animationDuration: '1.5s' }}></div>
                    <div className="absolute inset-4 rounded-full border-t-2 border-primary/40 animate-spin" style={{ animationDuration: '2s' }}></div>
                  </div>
                  <p className="text-muted-foreground animate-pulse">Loading your tests...</p>
                </div>
              ) : filteredTests.length > 0 ? (
                <motion.div
                  className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  {filteredTests.map((test) => (
                    <motion.div
                      key={test.id}
                      variants={itemVariants}
                      className="card-hover-effect"
                    >
                      <MockTestCard
                        mockTest={test}
                        onEditClick={(test) => {
                          setEditingTest(test);
                          setIsEditDialogOpen(true);
                        }}
                        onDeleteClick={(testId) => {
                          setDeletingTestId(testId);
                          setIsDeleteDialogOpen(true);
                        }}
                      />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="mt-8"
                >
                  <Alert className="bg-card border border-primary/10 shadow-md">
                    <AlertCircle className="h-5 w-5 text-primary" />
                    <AlertTitle className="text-lg font-semibold">No tests found</AlertTitle>
                    <AlertDescription className="text-muted-foreground">
                      {mockTests.length > 0
                        ? "No tests match your current filters. Try adjusting your search or filters."
                        : "Add your first mock test to start tracking your performance."}
                    </AlertDescription>
                  </Alert>
                </motion.div>
              )}
              </motion.div>
            </motion.div>
          </TabsContent>
        </Tabs>
        </motion.div>
        </div>

        {/* Edit Test Dialog */}
        <EditMockTestDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          mockTest={editingTest}
          userId={user.uid}
          onSave={handleRefreshTests}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent className="border border-destructive/20">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-xl">Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription className="text-muted-foreground">
                Are you sure you want to delete this mock test? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-2">
              <AlertDialogCancel className="border-primary/20">Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteTest}
                className="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}