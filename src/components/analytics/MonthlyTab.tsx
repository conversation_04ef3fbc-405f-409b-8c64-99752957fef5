import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/x-charts';
import { ArrowUp, ArrowDown, Calendar, Download, Clock, Target, Zap, Bar<PERSON>hart2, T<PERSON>dingUp, Bar<PERSON>hart3 } from "lucide-react";
import { Chart<PERSON>ie, ListChecks, Calendar as CalendarIcon } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useState, useMemo } from "react";

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
    taskTypeDurations?: { [key: string]: number };
  }[];
  weeklyStats: {
    weekNumber: number;
    year: number;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
    taskTypeDurations?: { [key: string]: number };
  }[];
  monthlyStats: {
    month: string;
    year: number;
    monthKey: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
    taskTypeDurations?: { [key: string]: number };
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
  taskTypeStats?: {
    taskType: string;
    totalDuration: number;
    sessionCount: number;
    averageSessionDuration: number;
    averageProductivityRating?: number;
  }[];
}

interface MonthlyTabProps {
  analytics: Analytics;
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  muiTheme: any;
}

const MonthlyTab: React.FC<MonthlyTabProps> = ({
  analytics,
  formatDuration,
  subjectColorMap,
  muiTheme
}) => {
  // Define colors array for subjects without assigned colors
  const COLORS = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316', '#10b981', '#06b6d4', '#3b82f6'];

  // State for selected period comparison
  const [selectedPeriod, setSelectedPeriod] = useState<"current" | "previous" | "year">("current");

  // Calculate month-over-month and year-over-year metrics
  const monthlyMetrics = useMemo(() => {
    if (!analytics?.monthlyStats.length) return null;

    const monthlyStats = [...analytics.monthlyStats].sort((a, b) => b.monthKey.localeCompare(a.monthKey));
    const currentMonth = monthlyStats[0];
    const previousMonth = monthlyStats.length > 1 ? monthlyStats[1] : null;

    // Find same month last year if available
    const sameMonthLastYear = monthlyStats.find(m =>
      m.month === currentMonth.month && m.year === currentMonth.year - 1
    );

    // Calculate month-over-month changes
    const momTimeChange = previousMonth && previousMonth.totalDuration > 0
      ? ((currentMonth.totalDuration - previousMonth.totalDuration) / previousMonth.totalDuration) * 100
      : null;

    const momPomChange = previousMonth && previousMonth.completedPomodoros > 0
      ? ((currentMonth.completedPomodoros - previousMonth.completedPomodoros) / previousMonth.completedPomodoros) * 100
      : null;

    // Calculate year-over-year changes
    const yoyTimeChange = sameMonthLastYear && sameMonthLastYear.totalDuration > 0
      ? ((currentMonth.totalDuration - sameMonthLastYear.totalDuration) / sameMonthLastYear.totalDuration) * 100
      : null;

    const yoyPomChange = sameMonthLastYear && sameMonthLastYear.completedPomodoros > 0
      ? ((currentMonth.completedPomodoros - sameMonthLastYear.completedPomodoros) / sameMonthLastYear.completedPomodoros) * 100
      : null;

    // Calculate daily average for current month, excluding future days
    let daysInMonth = 30; // Default fallback
    let daysToConsider = 30; // Days to consider for average calculation
    
    try {
      // First try with direct month number
      let monthIndex = parseInt(currentMonth.month) - 1;
      if (isNaN(monthIndex)) {
        // If month is a name like "January", get its index
        const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        monthIndex = monthNames.findIndex(name => name.toLowerCase() === currentMonth.month.toLowerCase());
        if (monthIndex === -1) monthIndex = 0; // Default to January if not found
      }
      
      // Get total days in the month
      daysInMonth = new Date(currentMonth.year, monthIndex + 1, 0).getDate();
      
      // Check if this is the current month
      const today = new Date();
      const isCurrentMonth = today.getMonth() === monthIndex && today.getFullYear() === currentMonth.year;
      
      if (isCurrentMonth) {
        // If it's the current month, only count days up to today
        daysToConsider = today.getDate();
      } else {
        // For past months, use all days
        daysToConsider = daysInMonth;
      }
    } catch (error) {
      console.warn("Error calculating days in month, using default values", error);
    }
    
    // Calculate average using only the days that have passed
    const dailyAvg = currentMonth.totalDuration / daysToConsider;

    // Count active subjects
    const currentSubjects = Object.keys(currentMonth.subjectDurations)
      .filter(subject => currentMonth.subjectDurations[subject] > 0).length;

    const previousSubjects = previousMonth
      ? Object.keys(previousMonth.subjectDurations)
        .filter(subject => previousMonth.subjectDurations[subject] > 0).length
      : 0;

    const subjectChange = previousMonth && previousSubjects > 0
      ? ((currentSubjects - previousSubjects) / previousSubjects) * 100
      : null;

    // Analyze the top performing subject
    const topSubject = Object.entries(currentMonth.subjectDurations)
      .sort(([, durationA], [, durationB]) => durationB - durationA)[0];

    // Calculate total productive hours this month (assuming 1 pomodoro = 25 minutes)
    const productiveHours = (currentMonth.completedPomodoros * 25) / 60;

    // Analyze task types if available
    let topTaskType = null;
    if (currentMonth.taskTypeDurations) {
      topTaskType = Object.entries(currentMonth.taskTypeDurations)
        .sort(([, durationA], [, durationB]) => durationB - durationA)[0];
    }

    return {
      currentMonth,
      previousMonth,
      sameMonthLastYear,
      momTimeChange,
      momPomChange,
      yoyTimeChange,
      yoyPomChange,
      dailyAvg,
      currentSubjects,
      previousSubjects,
      subjectChange,
      topSubject,
      productiveHours,
      topTaskType,
      daysInMonth
    };
  }, [analytics]);



  // Calculate subject distribution for the selected period
  const subjectDistribution = useMemo(() => {
    if (!analytics?.monthlyStats.length) return [];

    let targetMonth: typeof analytics.monthlyStats[0] | undefined;
    const sortedMonthsDesc = [...analytics.monthlyStats].sort((a, b) => b.monthKey.localeCompare(a.monthKey));

    // Select the appropriate month based on period
    if (selectedPeriod === "current") {
      targetMonth = sortedMonthsDesc[0]; // Current month
    } else if (selectedPeriod === "previous") {
      targetMonth = sortedMonthsDesc.length > 1 ? sortedMonthsDesc[1] : sortedMonthsDesc[0]; // Previous month or current if no previous
    } else { // "year"
      // Same month last year or current month if no data from last year
      targetMonth = sortedMonthsDesc.find(m =>
        m.month === sortedMonthsDesc[0].month && m.year === sortedMonthsDesc[0].year - 1
      ) || sortedMonthsDesc[0];
    }

    // Format data for pie chart
    return Object.entries(targetMonth.subjectDurations)
      .filter(([_, duration]) => (duration as number) > 0)
      .map(([subject, duration], index) => ({
        id: subject,
        value: (duration as number) / 3600, // Convert to hours
        label: subject,
        color: subjectColorMap[subject] || COLORS[index % COLORS.length],
      }));
  }, [analytics, selectedPeriod, subjectColorMap, COLORS]);
  
  // Calculate subject comparison data
  const subjectComparison = useMemo(() => {
    if (!analytics?.monthlyStats.length) return [];
    
    const sortedMonthsDesc = [...analytics.monthlyStats].sort((a, b) => b.monthKey.localeCompare(a.monthKey));
    const currentMonth = sortedMonthsDesc[0];
    const previousMonth = sortedMonthsDesc.length > 1 ? sortedMonthsDesc[1] : null;
    const sameMonthLastYear = sortedMonthsDesc.find(m => 
      m.month === currentMonth.month && m.year === currentMonth.year - 1
    );
    
    // Get all unique subjects across the periods we're comparing
    const allSubjects = new Set<string>();
    
    // Add subjects from current month
    Object.keys(currentMonth.subjectDurations).forEach(subject => allSubjects.add(subject));
    
    // Add subjects from previous month
    if (previousMonth) {
      Object.keys(previousMonth.subjectDurations).forEach(subject => allSubjects.add(subject));
    }
    
    // Add subjects from same month last year
    if (sameMonthLastYear) {
      Object.keys(sameMonthLastYear.subjectDurations).forEach(subject => allSubjects.add(subject));
    }
    
    return Array.from(allSubjects).map(subject => {
      const currentDuration = currentMonth.subjectDurations[subject] || 0;
      const previousDuration = previousMonth ? previousMonth.subjectDurations[subject] || 0 : 0;
      const yearAgoDuration = sameMonthLastYear ? sameMonthLastYear.subjectDurations[subject] || 0 : 0;
      
      const momChange = previousDuration > 0 
        ? ((currentDuration - previousDuration) / previousDuration) * 100 
        : null;
        
      const yoyChange = yearAgoDuration > 0
        ? ((currentDuration - yearAgoDuration) / yearAgoDuration) * 100
        : null;
      
      return {
        subject,
        currentDuration,
        previousDuration,
        yearAgoDuration,
        momChange,
        yoyChange,
        color: subjectColorMap[subject] || COLORS[Object.keys(subjectColorMap).indexOf(subject) % COLORS.length]
      };
    }).filter(item => item.currentDuration > 0 || item.previousDuration > 0 || item.yearAgoDuration > 0)
      .sort((a, b) => b.currentDuration - a.currentDuration);
  }, [analytics, subjectColorMap, COLORS]);

  // Calculate task type distribution
  const taskTypeDistribution = useMemo(() => {
    if (!analytics?.monthlyStats.length) return [];

    const currentMonth = [...analytics.monthlyStats].sort((a, b) => b.monthKey.localeCompare(a.monthKey))[0];

    if (!currentMonth.taskTypeDurations) return [];

    return Object.entries(currentMonth.taskTypeDurations)
      .filter(([_, duration]) => (duration as number) > 0)
      .map(([taskType, duration], index) => ({
        id: taskType,
        value: (duration as number) / 3600, // Convert to hours
        label: taskType,
        color: COLORS[(index + 3) % COLORS.length], // Offset to differentiate from subject colors
      }));
  }, [analytics, COLORS]);

  // Calculate monthly trends data
  const monthlyTrendsData = useMemo(() => {
    if (!analytics?.monthlyStats.length) return { xAxisData: [], series: [] };

    // Sort monthly stats by date in ascending order (oldest first)
    const sortedMonthlyStats = [...analytics.monthlyStats].sort((a, b) => 
      a.monthKey.localeCompare(b.monthKey)
    );

    // Format data for the line chart
    const xAxisData = sortedMonthlyStats.map(stat => `${stat.month} ${stat.year}`);
    
    // Total hours per month
    const totalHoursData = sortedMonthlyStats.map(stat => stat.totalDuration / 3600);
    
    // Pomodoros completed per month
    const pomodorosData = sortedMonthlyStats.map(stat => stat.completedPomodoros);

    // Calculate average daily study time per month
    const dailyAvgData = sortedMonthlyStats.map(stat => {
      let daysInMonth = 30; // Default fallback
      try {
        // First try with direct month number
        let monthIndex = parseInt(stat.month) - 1;
        if (isNaN(monthIndex)) {
          // If month is a name like "January", get its index
          const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
          monthIndex = monthNames.findIndex(name => name.toLowerCase() === stat.month.toLowerCase());
          if (monthIndex === -1) monthIndex = 0; // Default to January if not found
        }
        daysInMonth = new Date(stat.year, monthIndex + 1, 0).getDate();
      } catch (error) {
        console.warn("Error calculating days in month, using default 30", error);
      }
      return (stat.totalDuration / 3600) / daysInMonth;
    });

    return {
      xAxisData,
      series: [
        {
          data: totalHoursData,
          label: 'Total Hours',
          color: muiTheme.palette.primary.main,
          valueFormatter: (value: number) => {
            const seconds = value * 3600;
            return formatDuration(seconds);
          },
        },
        {
          data: dailyAvgData,
          label: 'Daily Average (hours)',
          color: '#f97316', // Orange
          valueFormatter: (value: number) => {
            return `${value.toFixed(1)} h/day`;
          },
        }
      ]
    };
  }, [analytics, formatDuration, muiTheme.palette.primary.main]);

  // Calculate comprehensive subject comparison data for the bar chart
  const subjectComparisonChartData = useMemo(() => {
    if (!analytics?.monthlyStats.length) return { subjects: [], currentMonth: [], previousMonth: [] };
    
    const sortedMonthsDesc = [...analytics.monthlyStats].sort((a, b) => b.monthKey.localeCompare(a.monthKey));
    const currentMonth = sortedMonthsDesc[0];
    const previousMonth = sortedMonthsDesc.length > 1 ? sortedMonthsDesc[1] : null;
    
    if (!previousMonth) return { subjects: [], currentMonth: [], previousMonth: [] };
    
    // Get all subjects that have data in either month
    const allSubjects = new Set<string>();
    Object.keys(currentMonth.subjectDurations).forEach(subject => allSubjects.add(subject));
    Object.keys(previousMonth.subjectDurations).forEach(subject => allSubjects.add(subject));
    
    // Create arrays for the chart data
    const subjects: string[] = [];
    const currentMonthData: number[] = [];
    const previousMonthData: number[] = [];
    
    // Sort subjects by current month duration (descending)
    const sortedSubjects = Array.from(allSubjects)
      .map(subject => ({
        name: subject,
        currentDuration: currentMonth.subjectDurations[subject] || 0,
        previousDuration: previousMonth.subjectDurations[subject] || 0,
      }))
      .filter(subject => subject.currentDuration > 0 || subject.previousDuration > 0)
      .sort((a, b) => b.currentDuration - a.currentDuration);
    
    // Take top 8 subjects for better visualization
    sortedSubjects.slice(0, 8).forEach(subject => {
      subjects.push(subject.name);
      currentMonthData.push(subject.currentDuration / 3600); // Convert to hours
      previousMonthData.push(subject.previousDuration / 3600); // Convert to hours
    });
    
    return {
      subjects,
      currentMonth: currentMonthData,
      previousMonth: previousMonthData,
      currentMonthName: currentMonth.month,
      previousMonthName: previousMonth.month,
      currentYear: currentMonth.year,
      previousYear: previousMonth.year
    };
  }, [analytics]);

  return (
    <>
      <div className="grid grid-cols-1 gap-6 mb-6">
        {/* Monthly Overview Card with Stats */}
        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
          <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <CalendarIcon className="h-5 w-5 text-primary" />
              <span>Monthly Progress Overview</span>
            </CardTitle>
            <div className="flex items-center gap-2">
              <button
                className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                title="Download chart"
              >
                <Download className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          </CardHeader>

          <CardContent className="pt-6">
            {/* Stats Cards */}
            {monthlyMetrics ? (
              <div className="space-y-6">
                {/* Month Summary */}
                <div className="p-4 bg-primary/5 rounded-xl border shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="text-sm text-muted-foreground">Current Month</h3>
                      <p className="text-xl font-semibold">
                        {monthlyMetrics.currentMonth.month} {monthlyMetrics.currentMonth.year}
                      </p>
                    </div>
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    <span className="font-medium">{monthlyMetrics.daysInMonth}</span> days total •
                    {monthlyMetrics.dailyAvg > 0
                      ? ` ${formatDuration(monthlyMetrics.dailyAvg)} avg. per day`
                      : ' No study activity yet'}
                  </p>
                </div>

                {/* Key Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Total Study Time */}
                  <div className="p-4 bg-primary/5 rounded-xl border shadow-sm flex flex-col justify-between">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-sm text-muted-foreground">Total Study Time</h3>
                        <p className="text-xl font-semibold">
                          {(monthlyMetrics.currentMonth.totalDuration / 3600).toFixed(1)}h
                        </p>
                      </div>
                      <div className="p-2 bg-primary/10 rounded-full">
                        <Clock className="h-5 w-5 text-primary" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      {/* MoM change */}
                      <div className={`flex items-center text-xs ${
                        monthlyMetrics.momTimeChange === null ? 'text-muted-foreground' :
                        monthlyMetrics.momTimeChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                      }`}>
                        {monthlyMetrics.momTimeChange !== null ? (
                          <>
                            {monthlyMetrics.momTimeChange >= 0
                              ? <ArrowUp className="h-3 w-3 mr-1" />
                              : <ArrowDown className="h-3 w-3 mr-1" />
                            }
                            <span>{Math.abs(monthlyMetrics.momTimeChange).toFixed(1)}% vs last month</span>
                          </>
                        ) : (
                          <span>No previous data</span>
                        )}
                      </div>

                      {/* YoY change */}
                      {monthlyMetrics.yoyTimeChange !== null && (
                        <div className={`flex items-center text-xs ${
                          monthlyMetrics.yoyTimeChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                        }`}>
                          {monthlyMetrics.yoyTimeChange >= 0
                            ? <ArrowUp className="h-3 w-3 mr-1" />
                            : <ArrowDown className="h-3 w-3 mr-1" />
                          }
                          <span>{Math.abs(monthlyMetrics.yoyTimeChange).toFixed(1)}% YoY</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Pomodoros Completed */}
                  <div className="p-4 bg-pink-500/5 rounded-xl border shadow-sm flex flex-col justify-between">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-sm text-muted-foreground">Pomodoros</h3>
                        <p className="text-xl font-semibold text-pink-500">
                          {monthlyMetrics.currentMonth.completedPomodoros}
                        </p>
                      </div>
                      <div className="p-2 bg-pink-500/10 rounded-full">
                        <Target className="h-5 w-5 text-pink-500" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      {/* MoM change */}
                      <div className={`flex items-center text-xs ${
                        monthlyMetrics.momPomChange === null ? 'text-muted-foreground' :
                        monthlyMetrics.momPomChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                      }`}>
                        {monthlyMetrics.momPomChange !== null ? (
                          <>
                            {monthlyMetrics.momPomChange >= 0
                              ? <ArrowUp className="h-3 w-3 mr-1" />
                              : <ArrowDown className="h-3 w-3 mr-1" />
                            }
                            <span>{Math.abs(monthlyMetrics.momPomChange).toFixed(1)}% vs last month</span>
                          </>
                        ) : (
                          <span>No previous data</span>
                        )}
                      </div>

                      {/* YoY change */}
                      {monthlyMetrics.yoyPomChange !== null && (
                        <div className={`flex items-center text-xs ${
                          monthlyMetrics.yoyPomChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                        }`}>
                          {monthlyMetrics.yoyPomChange >= 0
                            ? <ArrowUp className="h-3 w-3 mr-1" />
                            : <ArrowDown className="h-3 w-3 mr-1" />
                          }
                          <span>{Math.abs(monthlyMetrics.yoyPomChange).toFixed(1)}% YoY</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Active Subjects */}
                  <div className="p-4 bg-cyan-500/5 rounded-xl border shadow-sm flex flex-col justify-between">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-sm text-muted-foreground">Active Subjects</h3>
                        <p className="text-xl font-semibold text-cyan-500">
                          {monthlyMetrics.currentSubjects}
                        </p>
                      </div>
                      <div className="p-2 bg-cyan-500/10 rounded-full">
                        <BarChart2 className="h-5 w-5 text-cyan-500" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      {/* Subject change */}
                      <div className={`flex items-center text-xs ${
                        monthlyMetrics.subjectChange === null ? 'text-muted-foreground' :
                        monthlyMetrics.subjectChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                      }`}>
                        {monthlyMetrics.subjectChange !== null ? (
                          <>
                            {monthlyMetrics.subjectChange >= 0
                              ? <ArrowUp className="h-3 w-3 mr-1" />
                              : <ArrowDown className="h-3 w-3 mr-1" />
                            }
                            <span>{Math.abs(monthlyMetrics.subjectChange).toFixed(1)}% vs last month</span>
                          </>
                        ) : (
                          <span>No previous data</span>
                        )}
                      </div>

                      {/* Top subject */}
                      {monthlyMetrics.topSubject && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Top: </span>
                          <span className="font-medium">{monthlyMetrics.topSubject[0]}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Productive Hours */}
                  <div className="p-4 bg-amber-500/5 rounded-xl border shadow-sm flex flex-col justify-between">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-sm text-muted-foreground">Productive Hours</h3>
                        <p className="text-xl font-semibold text-amber-500">
                          {monthlyMetrics.productiveHours.toFixed(1)}h
                        </p>
                      </div>
                      <div className="p-2 bg-amber-500/10 rounded-full">
                        <Zap className="h-5 w-5 text-amber-500" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      {/* Productive hours info */}
                      <div className="text-xs text-muted-foreground">
                        Based on {monthlyMetrics.currentMonth.completedPomodoros} completed pomodoros
                      </div>

                      {/* Top task type if available */}
                      {monthlyMetrics.topTaskType && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Top task: </span>
                          <span className="font-medium">{monthlyMetrics.topTaskType[0]}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                <p className="text-muted-foreground">No monthly data available yet.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add Monthly Trends Chart */}
        <div className="grid grid-cols-1 gap-6 mb-6">
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
            <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                <span>Monthly Trends</span>
              </CardTitle>
              <div className="flex items-center gap-2">
                <button
                  className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                  title="Download chart"
                >
                  <Download className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="h-[400px] pt-6">
              {monthlyTrendsData.xAxisData.length > 0 ? (
                <ThemeProvider theme={muiTheme}>
                  <LineChart
                    className="chart-with-theme"
                    sx={{
                      '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                        fill: muiTheme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                      }
                    }}
                    xAxis={[{
                      data: monthlyTrendsData.xAxisData,
                      scaleType: 'band',
                      label: 'Month',
                    }]}
                    yAxis={[{
                      label: 'Study Time (hours)',
                      min: 0,
                    }]}
                    series={monthlyTrendsData.series}
                    height={350}
                    margin={{ top: 20, right: 30, bottom: 80, left: 70 }}
                    slotProps={{
                      legend: {
                        direction: 'row',
                        position: { vertical: 'bottom', horizontal: 'middle' },
                        padding: 10,
                        itemMarkWidth: 10,
                        itemMarkHeight: 10,
                        markGap: 5,
                        itemGap: 15,
                      },
                    }}
                  />
                </ThemeProvider>
              ) : (
                <div className="flex flex-col items-center justify-center h-full py-12">
                  <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                  <p className="text-muted-foreground text-center">No monthly data available yet.<br />Start studying to see your progress!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Subject Distribution and Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Subject Distribution Card */}
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
            <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <ChartPie className="h-5 w-5 text-primary" />
                <span>Subject Distribution</span>
              </CardTitle>
              <div className="flex items-center gap-2">
                <select
                  className="text-xs bg-muted px-2 py-1 rounded-md border text-muted-foreground"
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value as any)}
                >
                  <option value="current">Current Month</option>
                  <option value="previous">Previous Month</option>
                  <option value="year">Same Month Last Year</option>
                </select>
              </div>
            </CardHeader>
            <CardContent className="h-[400px] pt-6">
              {subjectDistribution.length > 0 ? (
                <ThemeProvider theme={muiTheme}>
                  <PieChart
                    className="chart-with-theme"
                    series={[
                      {
                        data: subjectDistribution,
                        highlightScope: { faded: 'global', highlighted: 'item' },
                        faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                        valueFormatter: (value: number) => formatDuration(Number(value) * 3600),
                      },
                    ] as any}
                    height={350}
                    margin={{ top: 10, bottom: 10, left: 10, right: 60 }}
                    slotProps={{
                      legend: {
                        direction: 'column',
                        position: { vertical: 'middle', horizontal: 'right' },
                        padding: 0,
                        itemMarkWidth: 8,
                        itemMarkHeight: 8,
                        markGap: 5,
                        itemGap: 12,
                      },
                    }}
                  />
                </ThemeProvider>
              ) : (
                <div className="flex flex-col items-center justify-center h-full">
                  <ChartPie className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                  <p className="text-muted-foreground">No subject data available.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Task Type Analysis or Top Subjects */}
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
            <Tabs defaultValue="subjects" className="w-full">
              <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
                <TabsList>
                  <TabsTrigger value="subjects" className="text-sm">Top Subjects</TabsTrigger>
                  <TabsTrigger value="comparison" className="text-sm">Subject Comparison</TabsTrigger>
                  <TabsTrigger value="taskTypes" className="text-sm">Task Types</TabsTrigger>
                </TabsList>
              </CardHeader>

              <CardContent className="pt-6">
                <TabsContent value="subjects" className="mt-0">
                  {monthlyMetrics ? (
                    <div className="space-y-6">
                      <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/5 rounded-xl border backdrop-blur-sm">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary"></div>
                            <span className="text-sm font-medium">
                              {monthlyMetrics.currentMonth.month} {monthlyMetrics.currentMonth.year}
                            </span>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Top performing subjects this month by study time
                        </p>
                      </div>

                      <div className="space-y-4">
                        {Object.entries(monthlyMetrics.currentMonth.subjectDurations)
                          .filter(([_, duration]) => duration > 0)
                          .sort(([_, durationA], [__, durationB]) => durationB - durationA)
                          .slice(0, 5)
                          .map(([subject, duration], index) => {
                            // Calculate percentage of month's total study time
                            const percentOfTotal = monthlyMetrics.currentMonth.totalDuration > 0
                              ? (duration / monthlyMetrics.currentMonth.totalDuration) * 100
                              : 0;

                            // Check if this subject existed in previous month
                            const prevDuration = monthlyMetrics.previousMonth
                              ? (monthlyMetrics.previousMonth.subjectDurations[subject] || 0)
                              : 0;

                            const change = prevDuration > 0
                              ? ((duration - prevDuration) / prevDuration) * 100
                              : null;

                            return (
                              <div key={subject} className="p-4 bg-muted/30 rounded-lg border">
                                <div className="flex justify-between items-center mb-2">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-4 h-4 rounded-md"
                                      style={{ backgroundColor: subjectColorMap[subject] || COLORS[index % COLORS.length] }}
                                    />
                                    <h4 className="font-medium">{subject}</h4>
                                  </div>
                                  <div className="flex items-center">
                                    {change !== null && (
                                      <span className={`text-xs mr-2 ${
                                        change >= 0 ? 'text-emerald-500' : 'text-red-500'
                                      }`}>
                                        {change >= 0 ? '↑' : '↓'} {Math.abs(change).toFixed(0)}%
                                      </span>
                                    )}
                                    <span className="text-sm font-semibold">
                                      {formatDuration(duration)}
                                    </span>
                                  </div>
                                </div>
                                <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                                  <div
                                    className="h-full rounded-full"
                                    style={{
                                      width: `${percentOfTotal}%`,
                                      backgroundColor: subjectColorMap[subject] || COLORS[index % COLORS.length]
                                    }}
                                  />
                                </div>
                                <div className="flex justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">{percentOfTotal.toFixed(1)}% of total</span>
                                  <span className="text-xs text-muted-foreground">
                                    {prevDuration > 0
                                      ? `vs. ${formatDuration(prevDuration)} last month`
                                      : 'New subject this month'}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <ListChecks className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                      <p className="text-muted-foreground">No subject data available.</p>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="comparison" className="mt-0">
                  {subjectComparison.length > 0 ? (
                    <div className="space-y-6">
                      <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/5 rounded-xl border backdrop-blur-sm">
                        <div className="flex justify-between items-center mb-1">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary"></div>
                            <span className="text-sm font-medium">Subject Comparison</span>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-1 text-xs text-muted-foreground mt-2">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 rounded-full bg-primary"></div>
                            <span>Current Month</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
                            <span>Previous Month</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                            <span>Same Month Last Year</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4 max-h-[340px] overflow-y-auto pr-2">
                        {subjectComparison.slice(0, 8).map((item) => {
                          return (
                            <div key={item.subject} className="p-4 bg-muted/30 rounded-lg border">
                              <div className="flex justify-between items-center mb-2">
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-4 h-4 rounded-md"
                                    style={{ backgroundColor: item.color }}
                                  />
                                  <h4 className="font-medium">{item.subject}</h4>
                                </div>
                                <div className="flex items-center gap-3">
                                  {item.momChange !== null && (
                                    <span className={`text-xs flex items-center ${
                                      item.momChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                                    }`}>
                                      {item.momChange >= 0 
                                        ? <ArrowUp className="h-3 w-3 mr-0.5" />
                                        : <ArrowDown className="h-3 w-3 mr-0.5" />
                                      }
                                      <span>{Math.abs(item.momChange).toFixed(0)}% MoM</span>
                                    </span>
                                  )}
                                  {item.yoyChange !== null && (
                                    <span className={`text-xs flex items-center ${
                                      item.yoyChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                                    }`}>
                                      {item.yoyChange >= 0 
                                        ? <ArrowUp className="h-3 w-3 mr-0.5" />
                                        : <ArrowDown className="h-3 w-3 mr-0.5" />
                                      }
                                      <span>{Math.abs(item.yoyChange).toFixed(0)}% YoY</span>
                                    </span>
                                  )}
                                </div>
                              </div>
                              
                              {/* Current Month Bar */}
                              <div className="flex items-center mb-1">
                                <div className="w-24 flex-shrink-0">
                                  <span className="text-xs font-medium">Current</span>
                                </div>
                                <div className="flex-1 h-4 bg-muted rounded-full overflow-hidden">
                                  <div
                                    className="h-full rounded-full bg-primary"
                                    style={{
                                      width: `${item.currentDuration > 0 ? '100%' : '0%'}`
                                    }}
                                  />
                                </div>
                                <div className="ml-2 w-24 text-right text-xs">
                                  {formatDuration(item.currentDuration)}
                                </div>
                              </div>
                              
                              {/* Previous Month Bar */}
                              <div className="flex items-center mb-1">
                                <div className="w-24 flex-shrink-0">
                                  <span className="text-xs font-medium">Previous</span>
                                </div>
                                <div className="flex-1 h-4 bg-muted rounded-full overflow-hidden">
                                  <div
                                    className="h-full rounded-full bg-muted-foreground/70"
                                    style={{
                                      width: `${item.previousDuration > 0 
                                        ? (item.previousDuration / Math.max(item.currentDuration, item.previousDuration, item.yearAgoDuration)) * 100 + '%' 
                                        : '0%'}`
                                    }}
                                  />
                                </div>
                                <div className="ml-2 w-24 text-right text-xs">
                                  {item.previousDuration > 0 
                                    ? formatDuration(item.previousDuration)
                                    : 'No data'}
                                </div>
                              </div>
                              
                              {/* Year Ago Bar */}
                              <div className="flex items-center">
                                <div className="w-24 flex-shrink-0">
                                  <span className="text-xs font-medium">Last Year</span>
                                </div>
                                <div className="flex-1 h-4 bg-muted rounded-full overflow-hidden">
                                  <div
                                    className="h-full rounded-full bg-amber-500/70"
                                    style={{
                                      width: `${item.yearAgoDuration > 0 
                                        ? (item.yearAgoDuration / Math.max(item.currentDuration, item.previousDuration, item.yearAgoDuration)) * 100 + '%'
                                        : '0%'}`
                                    }}
                                  />
                                </div>
                                <div className="ml-2 w-24 text-right text-xs">
                                  {item.yearAgoDuration > 0 
                                    ? formatDuration(item.yearAgoDuration)
                                    : 'No data'}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <BarChart2 className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                      <p className="text-muted-foreground">No subject comparison data available.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="taskTypes" className="mt-0">
                  {taskTypeDistribution.length > 0 ? (
                    <div className="space-y-6">
                      <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/5 rounded-xl border backdrop-blur-sm">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary"></div>
                            <span className="text-sm font-medium">Task Type Analysis</span>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Distribution of your study time by task type
                        </p>
                      </div>

                      <div className="h-[280px]">
                        <ThemeProvider theme={muiTheme}>
                          <BarChart
                            className="chart-with-theme"
                            dataset={taskTypeDistribution}
                            xAxis={[{
                              scaleType: 'band',
                              dataKey: 'label',
                              label: 'Task Type',
                            }]}
                            series={[
                              {
                                dataKey: 'value',
                                label: 'Hours',
                                valueFormatter: (value: number) => formatDuration(Number(value) * 3600),
                                color: muiTheme.palette.primary.main,
                              },
                            ]}
                            height={280}
                            margin={{ top: 10, bottom: 70, left: 40, right: 10 }}
                            layout="vertical"
                          />
                        </ThemeProvider>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <BarChart2 className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                      <p className="text-muted-foreground">No task type data available.</p>
                    </div>
                  )}
                </TabsContent>
              </CardContent>
            </Tabs>
          </Card>
        </div>

        {/* NEW SECTION: Subject Month-on-Month Comparison */}
        <div className="grid grid-cols-1 gap-6 mb-6">
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
            <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <span>Subject Month-on-Month Comparison</span>
              </CardTitle>
              <div className="flex items-center gap-2">
                <button
                  className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                  title="Download chart"
                >
                  <Download className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="h-[480px] pt-6">
              {subjectComparisonChartData.subjects.length > 0 ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/5 rounded-xl border backdrop-blur-sm">
                    <div className="flex justify-between items-center mb-1">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-primary"></div>
                        <span className="text-sm font-medium">Study Hours by Subject</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-1 text-xs text-muted-foreground mt-2">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <span>{subjectComparisonChartData.currentMonthName} {subjectComparisonChartData.currentYear}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
                        <span>{subjectComparisonChartData.previousMonthName} {subjectComparisonChartData.previousYear}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="h-[380px]">
                    <ThemeProvider theme={muiTheme}>
                      <BarChart
                        className="chart-with-theme"
                        sx={{
                          '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                            fill: muiTheme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                          }
                        }}
                        series={[
                          {
                            data: subjectComparisonChartData.currentMonth,
                            label: `${subjectComparisonChartData.currentMonthName} ${subjectComparisonChartData.currentYear}`,
                            color: muiTheme.palette.primary.main,
                            valueFormatter: (value: number) => formatDuration(Number(value) * 3600),
                          },
                          {
                            data: subjectComparisonChartData.previousMonth,
                            label: `${subjectComparisonChartData.previousMonthName} ${subjectComparisonChartData.previousYear}`,
                            color: muiTheme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
                            valueFormatter: (value: number) => formatDuration(Number(value) * 3600),
                          },
                        ]}
                        xAxis={[{
                          data: subjectComparisonChartData.subjects,
                          scaleType: 'band',
                          label: 'Subject',
                        }]}
                        yAxis={[{
                          label: 'Study Hours',
                          min: 0,
                        }]}
                        layout="vertical"
                        height={340}
                        margin={{ top: 10, bottom: 90, left: 60, right: 20 }}
                        slotProps={{
                          legend: {
                            direction: 'row',
                            position: { vertical: 'bottom', horizontal: 'middle' },
                            padding: 20,
                            itemMarkWidth: 10,
                            itemMarkHeight: 10,
                            markGap: 5,
                            itemGap: 15,
                          },
                        }}
                      />
                    </ThemeProvider>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full py-12">
                  <BarChart3 className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                  <p className="text-muted-foreground text-center">
                    No comparison data available yet.<br />
                    Study across multiple months to see subject comparison.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default MonthlyTab;
