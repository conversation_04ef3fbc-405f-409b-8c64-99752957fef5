import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/x-charts';
import { ArrowUp, ArrowDown, Clock, Calendar, Download } from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as Line<PERSON><PERSON>I<PERSON>, TrendingUp } from 'lucide-react';

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  weeklyStats: {
    weekNumber: number;
    year: number;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  monthlyStats: {
    month: string;
    year: number;
    monthKey: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
}

interface WeeklyTabProps {
  analytics: Analytics;
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  theme: string;
  muiTheme: any;
}

const WeeklyTab: React.FC<WeeklyTabProps> = ({
  analytics,
  formatDuration,
  subjectColorMap,
  theme,
  muiTheme
}) => {
  // Define colors array for subjects without assigned colors
  const COLORS = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316', '#10b981', '#06b6d4', '#3b82f6'];

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
        {/* Weekly Overview Card */}
        <div className="lg:col-span-1">
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden h-full">
            <CardHeader className="border-b pb-3">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <BarChart2 className="h-5 w-5 text-primary" />
                <span>Weekly Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6 space-y-6">
              {analytics?.weeklyStats.length ? (
                <>
                  {/* Get the most recent week */}
                  {(() => {
                    const latestWeek = analytics.weeklyStats[analytics.weeklyStats.length - 1];
                    return (
                      <>
                        <div className="flex items-center justify-between px-3 py-4 bg-muted/30 border rounded-lg backdrop-blur-sm">
                          <div className="flex items-center gap-3">
                            <div className="p-2.5 bg-primary/10 rounded-full">
                              <Clock className="h-5 w-5 text-primary" />
                            </div>
                            <span className="text-sm font-medium">Week {latestWeek.weekNumber}, {latestWeek.year}</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                          <span className="text-sm font-medium">Total Hours</span>
                          <span className="text-xl font-bold text-primary">{(latestWeek.totalDuration / 3600).toFixed(1)}h</span>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                          <span className="text-sm font-medium">Pomodoros</span>
                          <span className="text-xl font-bold text-pink-500">{latestWeek.completedPomodoros}</span>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                          <span className="text-sm font-medium">Subjects</span>
                          <span className="text-xl font-bold text-cyan-500">
                            {Object.keys(latestWeek.subjectDurations).filter(
                              subject => latestWeek.subjectDurations[subject] > 0
                            ).length}
                          </span>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                          <span className="text-sm font-medium">Daily Average</span>
                          <span className="text-xl font-bold text-amber-500">
                            {(latestWeek.totalDuration / 3600 / 7).toFixed(1)}h
                          </span>
                        </div>
                      </>
                    );
                  })()}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center h-full">
                  <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                  <p className="text-muted-foreground">No weekly data available.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Main Weekly Chart - Takes up 3 columns */}
        <div className="lg:col-span-3">
          <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden h-full">
            <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <BarChart2 className="h-5 w-5 text-primary" />
                <span>Weekly Progress</span>
              </CardTitle>
              <div className="flex items-center gap-2">
                <button
                  className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                  title="Switch chart type"
                >
                  <LineChartIcon className="h-4 w-4 text-muted-foreground" />
                </button>
                <button
                  className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                  title="Download chart"
                >
                  <Download className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="h-[400px] md:h-[450px] pt-6">
              <ThemeProvider theme={muiTheme}>
                <BarChart
                  className="chart-with-theme"
                  sx={{
                    '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                      fill: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                    }
                  }}
                  xAxis={[{
                    data: analytics?.weeklyStats.map(stat => `W${stat.weekNumber}`) || [],
                    scaleType: 'band',
                    label: 'Week Number',
                  }]}
                  yAxis={[{
                    label: 'Study Time (hours)',
                    min: 0,
                  }]}
                  series={[
                    {
                      data: analytics?.weeklyStats.map(stat => stat.totalDuration / 3600) || [],
                      label: 'Total Time (hours)',
                      color: muiTheme.palette.primary.main,
                      valueFormatter: (value) => {
                        const seconds = value * 3600;
                        return formatDuration(seconds);
                      },
                    },
                    {
                      data: analytics?.weeklyStats.map(stat => stat.completedPomodoros / 4) || [], // Scale for better visualization
                      label: 'Completed Pomodoros',
                      color: muiTheme.palette.secondary.main,
                      valueFormatter: (value) => {
                        return `${Math.round(value * 4)} pomodoros`;
                      },
                    },
                  ]}
                  height={350}
                  margin={{ top: 20, right: 30, bottom: 80, left: 70 }} // Further increased bottom margin
                  slotProps={{
                    legend: {
                      direction: 'row',
                      position: { vertical: 'bottom', horizontal: 'middle' },
                      padding: 10,
                      itemMarkWidth: 10,
                      itemMarkHeight: 10,
                      markGap: 5,
                      itemGap: 15,
                    },
                  }}
                />
              </ThemeProvider>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Subject Distribution Chart */}
        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
          <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <ChartPie className="h-5 w-5 text-pink-500" />
              <span>Weekly Subject Distribution</span>
            </CardTitle>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground px-2 py-1 bg-muted rounded-md">
                Last 4 Weeks
              </span>
              <button
                className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                title="Download chart"
              >
                <Download className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          </CardHeader>
          <CardContent className="h-[500px] pt-6">
            <ThemeProvider theme={muiTheme}>
              <BarChart
                className="chart-with-theme"
                sx={{
                  '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                    fill: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                  }
                }}
                xAxis={[{
                  data: analytics?.weeklyStats.slice(-4).map(stat => `W${stat.weekNumber}`) || [],
                  scaleType: 'band',
                }]}
                yAxis={[{
                  label: 'Study Time (hours)',
                  min: 0,
                }]}
                series={
                  analytics?.subjectStats.map((subject, index) => ({
                    data: analytics.weeklyStats.slice(-4).map(week =>
                      (week.subjectDurations[subject.subject] || 0) / 3600
                    ),
                    label: subject.subject,
                    color: subjectColorMap[subject.subject] || COLORS[index % COLORS.length],
                    stack: 'total',
                    valueFormatter: (value) => {
                      const seconds = value * 3600;
                      return formatDuration(seconds);
                    },
                  })) || []
                }
                height={450}
                margin={{ top: 20, right: 30, bottom: 80, left: 70 }} // Further increased bottom margin
                slotProps={{
                  legend: {
                    direction: 'row',
                    position: { vertical: 'bottom', horizontal: 'middle' },
                    padding: 10,
                    itemMarkWidth: 10,
                    itemMarkHeight: 10,
                    markGap: 5,
                    itemGap: 15,
                  },
                }}
              />
            </ThemeProvider>
          </CardContent>
        </Card>

        {/* Weekly Progress Comparison */}
        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
          <CardHeader className="border-b pb-3">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-emerald-500" />
              <span>Weekly Comparison</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            {analytics?.weeklyStats.length > 1 ? (
              <div className="space-y-6">
                {(() => {
                  // Get the last two weeks
                  const latestWeek = analytics.weeklyStats[analytics.weeklyStats.length - 1];
                  const previousWeek = analytics.weeklyStats[analytics.weeklyStats.length - 2];

                  // Calculate change percentages
                  const durChange = previousWeek.totalDuration > 0
                    ? ((latestWeek.totalDuration - previousWeek.totalDuration) / previousWeek.totalDuration) * 100
                    : 100;

                  const pomChange = previousWeek.completedPomodoros > 0
                    ? ((latestWeek.completedPomodoros - previousWeek.completedPomodoros) / previousWeek.completedPomodoros) * 100
                    : 100;

                  // Count subjects with more than 0 duration
                  const latestSubjects = Object.keys(latestWeek.subjectDurations)
                    .filter(subject => latestWeek.subjectDurations[subject] > 0).length;

                  const previousSubjects = Object.keys(previousWeek.subjectDurations)
                    .filter(subject => previousWeek.subjectDurations[subject] > 0).length;

                  const subjectChange = previousSubjects > 0
                    ? ((latestSubjects - previousSubjects) / previousSubjects) * 100
                    : 100;

                  return (
                    <>
                      <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/5 rounded-xl border backdrop-blur-sm">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary"></div>
                            <span className="text-sm font-medium">Week {latestWeek.weekNumber} vs Week {previousWeek.weekNumber}</span>
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Comparing your latest study week to the previous one
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        {/* Study Duration Change */}
                        <div className="p-4 bg-muted/30 rounded-lg border flex flex-col">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Study Time</span>
                            <div className={`flex items-center text-sm font-medium ${
                              durChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                            }`}>
                              {durChange >= 0 ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
                              {Math.abs(durChange).toFixed(1)}%
                            </div>
                          </div>
                          <div className="text-2xl font-bold">{(latestWeek.totalDuration / 3600).toFixed(1)}h</div>
                          <div className="text-sm text-muted-foreground mt-1">Previous: {(previousWeek.totalDuration / 3600).toFixed(1)}h</div>
                        </div>

                        {/* Pomodoros Change */}
                        <div className="p-4 bg-muted/30 rounded-lg border flex flex-col">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Pomodoros</span>
                            <div className={`flex items-center text-sm font-medium ${
                              pomChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                            }`}>
                              {pomChange >= 0 ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
                              {Math.abs(pomChange).toFixed(1)}%
                            </div>
                          </div>
                          <div className="text-2xl font-bold">{latestWeek.completedPomodoros}</div>
                          <div className="text-sm text-muted-foreground mt-1">Previous: {previousWeek.completedPomodoros}</div>
                        </div>

                        {/* Subjects Change */}
                        <div className="p-4 bg-muted/30 rounded-lg border flex flex-col">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Subjects</span>
                            <div className={`flex items-center text-sm font-medium ${
                              subjectChange >= 0 ? 'text-emerald-500' : 'text-red-500'
                            }`}>
                              {subjectChange >= 0 ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
                              {Math.abs(subjectChange).toFixed(1)}%
                            </div>
                          </div>
                          <div className="text-2xl font-bold">{latestSubjects}</div>
                          <div className="text-sm text-muted-foreground mt-1">Previous: {previousSubjects}</div>
                        </div>
                      </div>

                      {/* Top Subjects Comparison */}
                      <div className="p-4 bg-muted/30 rounded-lg border">
                        <h3 className="text-sm font-medium mb-3">Most Studied Subjects</h3>
                        <div className="space-y-3">
                          {Object.entries(latestWeek.subjectDurations)
                            .filter(([_, duration]) => duration > 0)
                            .sort(([_, durationA], [__, durationB]) => durationB - durationA)
                            .slice(0, 3)
                            .map(([subject, duration], index) => {
                              const prevDuration = previousWeek.subjectDurations[subject] || 0;
                              const change = prevDuration > 0
                                ? ((duration - prevDuration) / prevDuration) * 100
                                : 100;

                              return (
                                <div key={subject} className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-md"
                                      style={{ backgroundColor: subjectColorMap[subject] || COLORS[index % COLORS.length] }}
                                    ></div>
                                    <span className="text-sm">{subject}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">{formatDuration(duration)}</span>
                                    <span className={`text-xs ${
                                      change >= 0 ? 'text-emerald-500' : 'text-red-500'
                                    }`}>
                                      {change >= 0 ? '↑' : '↓'} {Math.abs(change).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                              );
                            })
                          }
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[400px] text-center">
                <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                <p className="text-muted-foreground">Not enough data for weekly comparison.</p>
                <p className="text-xs text-muted-foreground mt-2">Need at least two weeks of data.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default WeeklyTab;
