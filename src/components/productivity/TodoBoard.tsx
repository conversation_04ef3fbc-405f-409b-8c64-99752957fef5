import { useEffect, useState } from 'react';
import { DragDropContext, DropResult, DragStart, DragUpdate } from '@hello-pangea/dnd';
import { TodoColumn } from './TodoColumn';
import { TodoTable } from './TodoTable';
import { AddTaskButton } from './AddTaskButton';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Plus, LayoutGrid, List, AlertCircle } from 'lucide-react';
import { TodoItem } from '@/types/todo';
import { startOfDay } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

interface TodoBoardProps {
  groupId?: string;
}

export function TodoBoard({ groupId, compact = false }: TodoBoardProps & { compact?: boolean }) {
  const { board, fetchTodos, moveTask, addColumn, loading } = useSupabaseTodoStore();
  const { user } = useSupabaseAuth();

  const [isAddingColumn, setIsAddingColumn] = useState(false);
  const [newColumnTitle, setNewColumnTitle] = useState('');
  const [viewMode, setViewMode] = useState<'kanban' | 'table'>(compact ? 'table' : 'kanban');
  const [activeDragId, setActiveDragId] = useState<string | null>(null);
  const [dragSourceColumn, setDragSourceColumn] = useState<string | null>(null);

  // Debug logging
  useEffect(() => {
    console.log('Current board state:', board);
  }, [board]);

  useEffect(() => {
    if (user) {
      console.log('Fetching todos for user:', user.uid, 'groupId:', groupId);
      // Fetch todos for the current user and group ID.
      // The fetchTodos function in the store should handle updating the state correctly,
      // replacing old data if necessary, without needing a manual reset here.
      fetchTodos(user.uid, groupId);

      return () => {
        // No cleanup needed here since fetchTodos handles its own cleanup
      };
    }
  }, [user, groupId, fetchTodos]);

  const handleDragStart = (start: DragStart) => {
    const { draggableId, source } = start;
    setActiveDragId(draggableId);
    setDragSourceColumn(source.droppableId);

    // Add a class to the body to indicate dragging is in progress
    document.body.classList.add('dragging-active');

    // Find the task element and add a class to it
    const taskElement = document.querySelector(`[data-rbd-draggable-id="${draggableId}"]`);
    if (taskElement) {
      taskElement.classList.add('task-being-dragged');
    }
  };

  const handleDragUpdate = (update: DragUpdate) => {
    // You can use this to provide additional visual feedback during dragging
    const { destination } = update;

    if (!destination) {
      // If dragging outside valid drop targets, you could show a visual indicator
      document.body.classList.add('dragging-invalid');
    } else {
      document.body.classList.remove('dragging-invalid');
    }
  };

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // Reset drag state
    setActiveDragId(null);
    setDragSourceColumn(null);

    // Remove any drag-related classes
    document.body.classList.remove('dragging-active', 'dragging-invalid');

    // Remove the class from the task element
    const taskElement = document.querySelector(`[data-rbd-draggable-id="${draggableId}"]`);
    if (taskElement) {
      taskElement.classList.remove('task-being-dragged');
    }

    // Log the drag operation for debugging
    console.log('Drag end event:', {
      draggableId,
      source,
      destination
    });

    // Dropped outside the list or no destination
    if (!destination) {
      console.log('Dropped outside a droppable area');
      return;
    }

    // Check if the task was dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      console.log('Dropped in the same position - no change needed');
      return;
    }

    try {
      // Create a modified source and destination that includes the draggableId
      const enhancedSource = {
        ...source,
        taskId: draggableId
      };

      // Handle dropping at the end of a column
      // If the destination index is greater than the number of tasks in the column,
      // adjust it to be at the end of the column
      const destColumn = board.columns[destination.droppableId];
      const adjustedDestination = {
        ...destination,
        index: Math.min(destination.index, destColumn.taskIds.length),
        taskId: draggableId
      };

      // Move the task using the enhanced source and destination
      moveTask(enhancedSource, adjustedDestination)
        .then(() => {
          // Show success toast for completed moves between different columns
          if (source.droppableId !== destination.droppableId) {
            const sourceColumn = board.columns[source.droppableId];
            const destColumn = board.columns[destination.droppableId];
            const taskTitle = board.tasks[draggableId]?.title || 'Task';

            toast({
              title: "Task moved",
              description: `"${taskTitle}" moved from ${sourceColumn.title} to ${destColumn.title}`,
              duration: 3000,
            });
          }
        })
        .catch(error => {
          console.error('Error moving task:', error);
          toast({
            title: "Error moving task",
            description: error.message || "There was a problem moving the task",
            variant: "destructive",
            duration: 5000,
          });
        });
    } catch (error) {
      console.error('Error in drag and drop operation:', error);
    }
  };

  const handleAddColumn = async () => {
    if (!newColumnTitle.trim()) return;

    await addColumn(newColumnTitle);

    // Reset form
    setNewColumnTitle('');
    setIsAddingColumn(false);
  };

  // Get all tasks from all columns for the table view
  const getAllTasks = (): TodoItem[] => {
    const allTasks: TodoItem[] = [];

    // Collect tasks from all columns
    Object.values(board.columns).forEach(column => {
      column.taskIds.forEach(taskId => {
        if (board.tasks[taskId]) {
          allTasks.push(board.tasks[taskId]);
        }
      });
    });

    return allTasks;
  };

  // Check if a task is overdue
  const isTaskOverdue = (task: TodoItem): boolean => {
    if (!task.dueDate) return false;

    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));

    // Task is overdue if:
    // 1. Due date is before today (not including today)
    // 2. Task is not in the "Done" column
    const isDone = board.columns['column-3'].taskIds.includes(task.id);

    return taskDueDate < today && !isDone;
  };

  // Count overdue tasks
  const overdueTasksCount = getAllTasks().filter(task => isTaskOverdue(task)).length;

  // Prevent event propagation to keep the hover menu open when interacting with the board
  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div
      className={`${compact ? 'h-[400px]' : 'h-full'} flex flex-col relative`}
      onMouseDown={handleMouseDown}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          {/* Theme-aware title */}
          <h2 className="text-xl font-bold text-foreground">Tasks</h2>
          {overdueTasksCount > 0 && (
            // Theme-aware overdue indicator
            <div className="flex items-center text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/40 px-2 py-1 rounded-md text-sm border border-red-200 dark:border-red-800">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span>{overdueTasksCount} overdue {overdueTasksCount === 1 ? 'task' : 'tasks'}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          {/* View toggle buttons - only show if not compact mode */}
          {!compact && (
            // Theme-aware border
            <div className="flex border border-border rounded-md overflow-hidden mr-2">
              {/* Theme-aware button */}
              <Button
                variant={viewMode === 'kanban' ? 'secondary' : 'ghost'} // Use secondary for active light mode
                size="sm"
                className="rounded-none data-[state=active]:bg-primary data-[state=active]:text-primary-foreground" // Keep dark active style if needed
                onClick={(e) => {
                  e.stopPropagation();
                  setViewMode('kanban');
                }}
              >
                <LayoutGrid className="h-4 w-4 mr-1" />
                Kanban
              </Button>
              {/* Theme-aware button */}
              <Button
                variant={viewMode === 'table' ? 'secondary' : 'ghost'} // Use secondary for active light mode
                size="sm"
                className="rounded-none data-[state=active]:bg-primary data-[state=active]:text-primary-foreground" // Keep dark active style if needed
                onClick={(e) => {
                  e.stopPropagation();
                  setViewMode('table');
                }}
              >
                <List className="h-4 w-4 mr-1" />
                Table
              </Button>
            </div>
          )}

          {/* Only show Add Column button in Kanban view and not in compact mode */}
          {viewMode === 'kanban' && !compact && (
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsAddingColumn(true);
              }}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Column
            </Button>
          )}

          <AddTaskButton groupId={groupId} />
        </div>
      </div>

      <div className="flex-grow overflow-x-auto" onClick={(e) => e.stopPropagation()}>
        {viewMode === 'kanban' ? (
          <DragDropContext
            onDragStart={handleDragStart}
            onDragUpdate={handleDragUpdate}
            onDragEnd={handleDragEnd}
            enableDefaultSensors={true}
          >
            <div className="flex gap-4 h-full pb-4">
              {board.columnOrder.map((columnId) => {
                const column = board.columns[columnId];
                const tasks = column.taskIds
                  .filter(taskId => board.tasks[taskId]) // Filter out any non-existent tasks
                  .map(taskId => board.tasks[taskId]);

                console.log(`Column ${columnId} has ${tasks.length} tasks after filtering`);

                const isDefaultColumn = ['column-1', 'column-2', 'column-3'].includes(columnId);
                const isActiveDragSource = dragSourceColumn === columnId;

                return (
                  <div key={column.id} className="w-72 flex-shrink-0">
                    <TodoColumn
                      column={column}
                      tasks={tasks}
                      isDefaultColumn={isDefaultColumn}
                      isActiveDragSource={isActiveDragSource}
                      activeDragId={activeDragId}
                    />
                  </div>
                );
              })}
            </div>
          </DragDropContext>
        ) : (
          <TodoTable tasks={getAllTasks()} />
        )}

        {/* Show loading indicator when fetching tasks */}
        {loading && (
          // Theme-aware loading overlay
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 dark:bg-background/50 backdrop-blur-sm z-10">
            <div className="flex flex-col items-center gap-2">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">Loading tasks...</p>
            </div>
          </div>
        )}
      </div>

      {/* Floating Action Button for adding tasks */}
      {!compact && (
        <div className="fixed bottom-6 right-6 md:hidden">
          <AddTaskButton groupId={groupId} />
        </div>
      )}

      {/* Add Column Dialog */}
      <Dialog open={isAddingColumn} onOpenChange={setIsAddingColumn}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Column</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="columnTitle" className="text-sm font-medium">
                Title
              </label>
              {/* Theme-aware Input */}
              <Input
                id="columnTitle"
                value={newColumnTitle}
                onChange={(e) => setNewColumnTitle(e.target.value)}
                placeholder="Column title"
                className="bg-muted border-border"
              />
            </div>
          </div>

          <DialogFooter>
            {/* Primary button (styles likely ok) */}
            <Button onClick={handleAddColumn}>Add Column</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
