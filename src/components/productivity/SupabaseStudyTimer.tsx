import { useEffect, useState, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { TimerDisplay } from "./TimerDisplay";
import { TimerControls } from "./TimerControls";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { saveStudySession, updateUserProfile } from "@/utils/supabase";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useSupabaseUserStore } from "@/stores/supabaseUserStore";
import { useSupabaseSubjectStore } from "@/stores/supabaseSubjectStore";
import { toast } from "@/components/ui/use-toast";
import { Settings, PictureInPicture, PictureInPicture2, BellRing, BellOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { v4 as uuidv4 } from 'uuid';

interface Subject {
  id: string;
  name: string;
  color: string;
}

interface StudySession {
  id: string;
  user_id: string;
  subject: string;
  task_name?: string;
  task_type: string;
  duration: number; // in seconds
  mode: string;
  phase?: string;
  completed: boolean;
  productivity_rating?: number;
  feedback?: string;
  start_time: string;
  end_time?: string;
  date: string; // YYYY-MM-DD format
  notes?: string;
}

export function SupabaseStudyTimer() {
  const { user } = useSupabaseAuth();
  const { userProfile, updateUserProfile: updateUserProfileStore } = useSupabaseUserStore();
  const { subjects, fetchSubjects } = useSupabaseSubjectStore();
  const navigate = useNavigate();

  // Timer state
  const [timeRemaining, setTimeRemaining] = useLocalStorage("timeRemaining", 1500); // 25 minutes
  const [isRunning, setIsRunning] = useLocalStorage("isRunning", false);
  const [mode, setMode] = useLocalStorage("mode", "pomodoro");
  const [currentPhase, setCurrentPhase] = useLocalStorage("currentPhase", "work");
  const [completedSessions, setCompletedSessions] = useLocalStorage("completedSessions", 0);
  const [timerStatus, setTimerStatus] = useLocalStorage("timerStatus", "idle");

  // Subject and task state
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [taskName, setTaskName] = useState("");
  const [taskType, setTaskType] = useLocalStorage("taskType", "Study");

  // Session tracking
  const sessionStartTimeRef = useRef<number | null>(null);
  const [sessionDuration, setSessionDuration] = useState(0);

  // Dialog states
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [isSessionSummaryOpen, setIsSessionSummaryOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Session summary state
  const [sessionFeedback, setSessionFeedback] = useState("");
  const [sessionProductivity, setSessionProductivity] = useState(3);
  const [sessionSummaryType, setSessionSummaryType] = useState<"complete" | "pause">("complete");

  // Settings state
  const [workDuration, setWorkDuration] = useLocalStorage("workDuration", 25);
  const [shortBreakDuration, setShortBreakDuration] = useLocalStorage("shortBreakDuration", 5);
  const [longBreakDuration, setLongBreakDuration] = useLocalStorage("longBreakDuration", 15);
  const [notificationsEnabled, setNotificationsEnabled] = useLocalStorage("notificationsEnabled", true);

  // Load subjects on component mount
  useEffect(() => {
    if (user) {
      fetchSubjects(user.id);
    }
  }, [user, fetchSubjects]);

  // Timer logic
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRunning && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            handleTimerComplete();
            return 0;
          }
          return prev - 1;
        });
        
        // Update session duration
        if (sessionStartTimeRef.current) {
          setSessionDuration(Math.floor((Date.now() - sessionStartTimeRef.current) / 1000));
        }
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeRemaining]);

  const handleTimerComplete = useCallback(() => {
    setIsRunning(false);
    setTimerStatus("idle");
    
    if (notificationsEnabled && "Notification" in window) {
      new Notification("Timer Complete!", {
        body: `${currentPhase === "work" ? "Work" : "Break"} session finished!`,
        icon: "/icon-192x192.png"
      });
    }

    if (currentPhase === "work") {
      // Work session completed
      setCompletedSessions(prev => prev + 1);
      setSessionSummaryType("complete");
      setIsSessionSummaryOpen(true);
    } else {
      // Break completed, start next work session
      setCurrentPhase("work");
      setTimeRemaining(workDuration * 60);
    }
  }, [currentPhase, notificationsEnabled, workDuration, setCompletedSessions, setIsRunning, setTimerStatus, setTimeRemaining]);

  const startTimer = () => {
    if (!selectedSubject) {
      toast({ 
        title: "Select a subject", 
        description: "Please select a subject first.", 
        variant: "destructive" 
      });
      return;
    }

    if (timerStatus === "idle") {
      setIsTaskDialogOpen(true);
      return;
    }

    if (timerStatus === "paused") {
      setIsRunning(true);
      setTimerStatus("running");
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
    setTimerStatus("paused");
    setSessionSummaryType("pause");
    setIsSessionSummaryOpen(true);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimerStatus("idle");
    setTimeRemaining(mode === "pomodoro" ? workDuration * 60 : 1500);
    setCurrentPhase("work");
    sessionStartTimeRef.current = null;
    setSessionDuration(0);
    setTaskName("");
  };

  const handleTaskDialogSubmit = () => {
    if (!taskName.trim()) {
      toast({ 
        title: "Enter task name", 
        description: "Please enter a task name.", 
        variant: "destructive" 
      });
      return;
    }

    setIsTaskDialogOpen(false);
    setIsRunning(true);
    setTimerStatus("running");
    sessionStartTimeRef.current = Date.now();
    setSessionDuration(0);
  };

  const handleSessionSummarySubmit = async () => {
    if (!user || !selectedSubject) return;

    try {
      if (sessionDuration > 0) {
        const sessionData: Omit<StudySession, 'id'> = {
          user_id: user.id,
          subject: selectedSubject.name,
          task_name: taskName,
          task_type: taskType,
          duration: sessionDuration,
          mode: mode,
          phase: currentPhase,
          completed: sessionSummaryType === "complete",
          productivity_rating: sessionProductivity,
          feedback: sessionFeedback,
          start_time: new Date(sessionStartTimeRef.current!).toISOString(),
          end_time: new Date().toISOString(),
          date: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
          notes: sessionFeedback,
        };

        await saveStudySession({
          ...sessionData,
          id: uuidv4(),
        });

        toast({
          title: sessionSummaryType === "complete" ? "Session completed!" : "Session paused",
          description: sessionSummaryType === "complete" 
            ? `You studied ${selectedSubject.name} for ${Math.floor(sessionDuration / 60)} minutes`
            : `Current duration: ${Math.floor(sessionDuration / 60)} minutes`
        });
      }

      setIsSessionSummaryOpen(false);
      setSessionFeedback("");
      setSessionProductivity(3);

      if (sessionSummaryType === "complete") {
        resetTimer();
      }
    } catch (error) {
      console.error('Error saving study session:', error);
      toast({
        title: "Error",
        description: "Failed to save study session",
        variant: "destructive"
      });
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Study Timer</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setNotificationsEnabled(!notificationsEnabled)}
          >
            {notificationsEnabled ? <BellRing className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
          </Button>
          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Timer Settings</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Work Duration (minutes)</Label>
                  <Input
                    type="number"
                    value={workDuration}
                    onChange={(e) => setWorkDuration(Number(e.target.value))}
                    min="1"
                    max="120"
                  />
                </div>
                <div>
                  <Label>Short Break (minutes)</Label>
                  <Input
                    type="number"
                    value={shortBreakDuration}
                    onChange={(e) => setShortBreakDuration(Number(e.target.value))}
                    min="1"
                    max="30"
                  />
                </div>
                <div>
                  <Label>Long Break (minutes)</Label>
                  <Input
                    type="number"
                    value={longBreakDuration}
                    onChange={(e) => setLongBreakDuration(Number(e.target.value))}
                    min="1"
                    max="60"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={() => setIsSettingsOpen(false)}>Save</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Subject Selection */}
      <div className="mb-6">
        <Label className="text-sm font-medium mb-2 block">Select Subject</Label>
        <Select
          value={selectedSubject?.id || ""}
          onValueChange={(value) => {
            const subject = subjects.find(s => s.id === value);
            setSelectedSubject(subject || null);
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choose a subject..." />
          </SelectTrigger>
          <SelectContent>
            {subjects.map((subject) => (
              <SelectItem key={subject.id} value={subject.id}>
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: subject.color }}
                  />
                  {subject.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Timer Display */}
      <TimerDisplay 
        timeRemaining={timeRemaining}
        currentPhase={currentPhase}
        mode={mode}
        formatTime={formatTime}
      />

      {/* Timer Controls */}
      <TimerControls
        isRunning={isRunning}
        timerStatus={timerStatus}
        onStart={startTimer}
        onPause={pauseTimer}
        onReset={resetTimer}
        disabled={!selectedSubject}
      />

      {/* Session Info */}
      {sessionDuration > 0 && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            Current session: {formatDuration(sessionDuration)}
            {taskName && ` - ${taskName}`}
          </p>
        </div>
      )}

      {/* Task Dialog */}
      <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>What are you working on?</DialogTitle>
            <DialogDescription>
              Enter the task you'll be focusing on during this study session.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Task Name</Label>
              <Input
                value={taskName}
                onChange={(e) => setTaskName(e.target.value)}
                placeholder="e.g., Chapter 5 - Calculus"
                autoFocus
              />
            </div>
            <div>
              <Label>Task Type</Label>
              <Select value={taskType} onValueChange={setTaskType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Study">Study</SelectItem>
                  <SelectItem value="Practice">Practice</SelectItem>
                  <SelectItem value="Review">Review</SelectItem>
                  <SelectItem value="Research">Research</SelectItem>
                  <SelectItem value="Assignment">Assignment</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTaskDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleTaskDialogSubmit}>
              Start Timer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Session Summary Dialog */}
      <Dialog open={isSessionSummaryOpen} onOpenChange={setIsSessionSummaryOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {sessionSummaryType === "complete" ? "Session Complete!" : "Session Paused"}
            </DialogTitle>
            <DialogDescription>
              How was your study session?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Productivity Rating (1-5)</Label>
              <Slider
                value={[sessionProductivity]}
                onValueChange={(value) => setSessionProductivity(value[0])}
                min={1}
                max={5}
                step={1}
                className="mt-2"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Poor</span>
                <span>Excellent</span>
              </div>
            </div>
            <div>
              <Label>Notes (optional)</Label>
              <Textarea
                value={sessionFeedback}
                onChange={(e) => setSessionFeedback(e.target.value)}
                placeholder="How did the session go? Any insights or challenges?"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSessionSummaryOpen(false)}>
              Skip
            </Button>
            <Button onClick={handleSessionSummarySubmit}>
              Save Session
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
