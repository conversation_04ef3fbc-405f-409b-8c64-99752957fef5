import { useEffect, useState, useRef, useCallback } from "react";
import { TimerDisplay } from "./TimerDisplay";
import { TimerControls } from "./TimerControls";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "@/components/ui/use-toast";
import { Settings, PictureInPicture, PictureInPicture2, BellRing, BellOff, Minimize2, Maximize2 } from "lucide-react"; // Added Bell icons and new PiP icons
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SubjectManager, Subject } from "./SubjectManager";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// Removed worker-timers import


// --- Notification Permission ---
type NotificationPermissionStatus = "default" | "granted" | "denied";


// Safari-specific PiP API declarations
declare global {
  interface HTMLVideoElement {
    webkitSupportsPresentationMode?: (mode: string) => boolean;
    webkitPresentationMode?: string;
    webkitSetPresentationMode?: (mode: string) => void;
  }

  interface HTMLCanvasElement {
    webkitCaptureStream?: () => MediaStream;
  }
}

interface TimerSettings {
  workDuration: number; // seconds
  shortBreakDuration: number; // seconds
  longBreakDuration: number; // seconds
  sessionsUntilLongBreak: number;
  notificationInterval: number; // minutes
}

type TimerStatus = "idle" | "running" | "paused";
type TimerMode = "pomodoro" | "stopwatch";
type PhaseType = "work" | "shortBreak" | "longBreak";

// --- sessionStorage Persistence ---
const SESSION_STORAGE_KEY = "studyTimerState";

interface SavedTimerState {
  status: TimerStatus;
  startTime: number | null;
  accumulatedPausedTime: number;
  pauseStartTime: number | null;
  currentPhase: PhaseType;
  completedSessions: number;
  mode: TimerMode;
  selectedSubject: string;
  saveTimestamp: number; // When the state was saved
}

interface StudyTimerProps {
  mode: TimerMode;
  groupSync?: { // Group sync logic is temporarily disabled during refactor
    groupId: string;
    state: {
      isRunning: boolean;
      startTime: number | null;
      timeRemaining: number; // Needs adaptation for new logic
      currentPhase: PhaseType;
      completedSessions: number;
      mode: TimerMode;
    };
    setState: (state: any) => Promise<void>;
  };
}

interface StudySession {
  userId: string;
  subject: string;
  taskName: string;
  taskType: string; // "Lecture", "Exercise", "Reading", "Practice", "Review", "Custom"
  taskDescription: string; // User-provided description for the session
  startTime: Date; // Timestamp of when this specific session segment started
  endTime: Date;
  duration: number; // Duration in seconds
  mode: TimerMode;
  phase: PhaseType;
  completed: boolean;
  date: string; // YYYY-MM-DD format
  weekNumber: number;
  month: string; // YYYY-MM format
  year: number;
  subjectColor?: string; // Optional color for the subject
}

const DEFAULT_SETTINGS: TimerSettings = {
  workDuration: 25 * 60,
  shortBreakDuration: 5 * 60,
  longBreakDuration: 15 * 60,
  sessionsUntilLongBreak: 4,
  notificationInterval: 60, // Default to 60 minutes
};



// Maximum reasonable time (24 hours in seconds)
const MAX_REASONABLE_TIME = 24 * 60 * 60;

// Validate time to prevent unrealistic values
const validateTime = (seconds: number): number => {
  if (typeof seconds !== 'number' || isNaN(seconds)) return 0;
  if (seconds < 0) return 0;
  return Math.min(seconds, MAX_REASONABLE_TIME);
};

const formatDuration = (seconds: number) => {
  const validSeconds = validateTime(seconds);
  const hours = Math.floor(validSeconds / 3600);
  const minutes = Math.floor((validSeconds % 3600) / 60);
  const secs = Math.round(validSeconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  }
  return `${secs}s`;
};

export function StudyTimer({ mode /*, groupSync */ }: StudyTimerProps) { // groupSync temporarily disabled
  // --- New Timer State ---
  const [timerStatus, setTimerStatus] = useState<TimerStatus>("idle");
  const [startTime, setStartTime] = useState<number | null>(null); // Timestamp when timer started/resumed (Date.now())
  const [accumulatedPausedTime, setAccumulatedPausedTime] = useState<number>(0); // Total ms spent paused
  const [displayTime, setDisplayTime] = useState<number>(0); // Time shown in UI (seconds), calculated from state
  const timerWorkerRef = useRef<Worker | null>(null);
  const pauseStartTimeRef = useRef<number | null>(null); // Track timestamp when pause began
  const sessionStartTimeRef = useRef<number | null>(null); // Track timestamp when the current *session* (work/stopwatch) started for saving

  // --- Other State ---
  const [taskName, setTaskName] = useState(""); // Keep task name if needed for saving
  const [taskType, setTaskType] = useState<string>("Lecture"); // Default task type
  const [taskDescription, setTaskDescription] = useState(""); // Task description
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [tempSettings, setTempSettings] = useState<TimerSettings>(DEFAULT_SETTINGS);
  const [currentPhase, setCurrentPhase] = useState<PhaseType>("work");
  const [completedSessions, setCompletedSessions] = useState(0);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [lastNotifiedInterval, setLastNotifiedInterval] = useState(0); // State for stopwatch interval notification
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermissionStatus>("default"); // Track permission status

  // Picture-in-Picture state
  const [isPipSupported, setIsPipSupported] = useState(false);
  const [isPipActive, setIsPipActive] = useState(false);
  const pipVideoRef = useRef<HTMLVideoElement | null>(null);
  const pipCanvasRef = useRef<HTMLCanvasElement | null>(null);

  // Settings
  const [settings, setSettings] = useLocalStorage<TimerSettings>(
    "timerSettings",
    DEFAULT_SETTINGS
  );

  const { user } = useAuth();

  // Internal state for mode to allow restoration (Moved up)
  const [modeInternal, setModeInternal] = useState(mode);
  useEffect(() => {
    setModeInternal(mode);
    // Reset timer when mode prop changes externally, potentially clear storage?
    // resetTimer(false); // Decide if mode change should reset and clear storage
  }, [mode]);

  // --- sessionStorage Helpers ---
  const saveStateToSessionStorage = useCallback(() => {
    const stateToSave: SavedTimerState = {
      status: timerStatus,
      startTime: startTime,
      accumulatedPausedTime: accumulatedPausedTime,
      pauseStartTime: pauseStartTimeRef.current,
      currentPhase: currentPhase,
      completedSessions: completedSessions,
      mode: mode, // Use original prop 'mode' here for saving consistency
      selectedSubject: selectedSubject?.name || "",
      saveTimestamp: Date.now(),
    };
    try {
      sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(stateToSave));
      // console.log("State saved to sessionStorage:", stateToSave);
    } catch (error) {
      console.error("Error saving state to sessionStorage:", error);
    }
  }, [timerStatus, startTime, accumulatedPausedTime, currentPhase, completedSessions, mode, selectedSubject]); // Dependencies needed for useCallback

  const loadStateFromSessionStorage = (): SavedTimerState | null => {
    try {
      const savedStateString = sessionStorage.getItem(SESSION_STORAGE_KEY);
      if (savedStateString) {
        const savedState = JSON.parse(savedStateString) as SavedTimerState;
        // Basic validation
        if (savedState && typeof savedState.status === 'string' && typeof savedState.saveTimestamp === 'number') {
           // console.log("State loaded from sessionStorage:", savedState);
           return savedState;
        }
      }
    } catch (error) {
      console.error("Error loading state from sessionStorage:", error);
    }
    return null;
  };

  const clearStateFromSessionStorage = () => {
    try {
      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      // console.log("State cleared from sessionStorage");
    } catch (error) {
      console.error("Error clearing state from sessionStorage:", error);
    }
  };

  // --- Firebase Saving Logic (Adapted) ---
  const getWeekNumber = (date: Date) => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  };

  const saveStudySession = async (duration: number, completed: boolean = true) => {
    // Use sessionStartTimeRef for the accurate start time of this segment
    if (!user || !sessionStartTimeRef.current || !selectedSubject || duration <= 0) {
        console.warn("Skipping saveStudySession due to missing data or zero duration", { user, sessionStartTime: sessionStartTimeRef.current, selectedSubject, duration });
        return;
    }

    const now = Date.now();
    const nowDate = new Date(now);

    // Format date in local time zone (YYYY-MM-DD)
    const formatDateToLocalYYYYMMDD = (date: Date): string => {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const session: StudySession = {
      userId: user.uid,
      subject: selectedSubject.name,
      taskName,
      taskType, // Use the selected task type
      taskDescription, // Use the task description
      startTime: new Date(sessionStartTimeRef.current), // Use the start of this specific session
      endTime: nowDate,
      duration: Math.round(validateTime(duration)), // Duration in seconds, validated
      mode: modeInternal, // Use internal mode state
      phase: currentPhase,
      completed,
      date: formatDateToLocalYYYYMMDD(nowDate), // Use local date format
      weekNumber: getWeekNumber(nowDate),
      month: formatDateToLocalYYYYMMDD(nowDate).slice(0, 7), // YYYY-MM format
      year: nowDate.getFullYear(),
      subjectColor: selectedSubject.color, // Save the subject color
    };

    try {
      const userRef = doc(db, "users", user.uid);
      const sessionId = sessionStartTimeRef.current.toString();
      await setDoc(userRef, { studySessions: { [sessionId]: session } }, { merge: true });
      // console.log("Study session saved:", sessionId, session);
      // Don't clear sessionStartTimeRef here, resetTimer or completion handles it
    } catch (error) {
      console.error("Error saving study session:", error);
      // Avoid toast for background saves, rely on console logs
      // toast({ title: "Save Error", description: "Could not save study session.", variant: "destructive" });
    }
  };

  // --- Pomodoro Phase Completion (Adapted & Moved Up) ---
  const handleTimerComplete = useCallback(() => { // Make useCallback
    if (modeInternal !== "pomodoro" || timerStatus !== 'running') return;

    const audio = new Audio("/notification.mp3");
    audio.play().catch(console.error);

    // Function to show browser notification (uses state now)
    const showCompletionNotification = (title: string, body: string) => {
      if (notificationPermission === 'granted') {
        new Notification(title, { body, icon: '/favicon.ico' });
      } else {
        console.log('Notification permission not granted or denied, skipping notification.');
      }
    };

    let nextPhase: PhaseType;
    let nextDuration: number;
    const newCompletedSessions = completedSessions + 1; // Calculate based on current state

    if (currentPhase === "work") {
      // Save completed work session *before* updating state
      // Duration should be the configured work duration, assuming it ran fully
      saveStudySession(settings.workDuration, true);

      // Determine next phase based on new session count
      let notificationTitle = "Work Complete!";
      let notificationBody = "";
      if (newCompletedSessions % settings.sessionsUntilLongBreak === 0) {
        nextPhase = "longBreak";
        nextDuration = settings.longBreakDuration;
        notificationBody = "Time for a long break! 🎉";
        toast({ title: notificationTitle, description: notificationBody });
      } else {
        nextPhase = "shortBreak";
        nextDuration = settings.shortBreakDuration;
        notificationBody = "Time for a short break! ☕";
        toast({ title: notificationTitle, description: notificationBody });
      }
      // Show browser notification
      showCompletionNotification(notificationTitle, notificationBody);
      // Update completed sessions state *after* determining phase
      setCompletedSessions(newCompletedSessions);

    } else { // Break finished
      nextPhase = "work";
      nextDuration = settings.workDuration;
      const notificationTitle = "Break's Over!";
      const notificationBody = "Back to work! 💪";
      toast({ title: notificationTitle, description: notificationBody });
      // Show browser notification for break completion as well
      showCompletionNotification(notificationTitle, notificationBody);
      // Don't increment completedSessions here
    }

    // Update state for the next phase
    const now = Date.now();
    setCurrentPhase(nextPhase);
    setDisplayTime(nextDuration);
    setStartTime(now);
    setAccumulatedPausedTime(0);
    sessionStartTimeRef.current = now; // Start new session segment
    pauseStartTimeRef.current = null;
    setTimerStatus("running"); // Ensure status is running

    // Save the state for the newly started phase
    saveStateToSessionStorage();

    // Ensure worker ticks continue (the effect depending on timerStatus will handle this)
    // if (timerWorkerRef.current) {
    //   timerWorkerRef.current.postMessage({ type: 'START' });
    // }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modeInternal, timerStatus, currentPhase, completedSessions, settings, saveStudySession, saveStateToSessionStorage]); // Add dependencies


  // --- Core Timer Calculation ---
  const calculateDisplayTime = useCallback(() => {
    // If idle, displayTime is set by reset/settings change, no calculation needed
    if (timerStatus === "idle") {
      return;
    }

    // If paused, calculate based on time up to the pause start
    if (timerStatus === "paused" && startTime !== null && pauseStartTimeRef.current !== null) {
      const elapsedMs = pauseStartTimeRef.current - startTime - accumulatedPausedTime; // Subtract accumulated pause time
      if (modeInternal === "pomodoro") {
        let phaseDurationSecs: number;
        switch (currentPhase) {
          case "work": phaseDurationSecs = settings.workDuration; break;
          case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
          case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
          default: phaseDurationSecs = settings.workDuration;
        }
        const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(elapsedMs / 1000));
        setDisplayTime(remainingSeconds);
      } else { // Stopwatch mode
        setDisplayTime(validateTime(Math.floor(elapsedMs / 1000)));
      }
      return; // Don't proceed to running calculation
    }

    // If running, calculate based on current time
    if (timerStatus === "running" && startTime !== null) {
      const now = Date.now();
      // Active elapsed time = (current time - start time) - total time spent paused
      const activeElapsedMs = now - startTime - accumulatedPausedTime;

      if (modeInternal === "pomodoro") {
        let phaseDurationSecs: number;
        switch (currentPhase) {
          case "work": phaseDurationSecs = settings.workDuration; break;
          case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
          case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
          default: phaseDurationSecs = settings.workDuration;
        }
        const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(activeElapsedMs / 1000));
        setDisplayTime(remainingSeconds);

        if (remainingSeconds <= 0) {
          handleTimerComplete(); // Automatically transition
        }
      } else { // Stopwatch mode
        const elapsedSeconds = Math.floor(activeElapsedMs / 1000);
        const validatedElapsedSeconds = validateTime(elapsedSeconds);
        setDisplayTime(validatedElapsedSeconds); // Validate stopwatch time

        // Stopwatch Custom Notification Interval Logic
        const notificationIntervalSeconds = settings.notificationInterval * 60; // Convert minutes to seconds
        const currentIntervalMark = Math.floor(validatedElapsedSeconds / notificationIntervalSeconds);
        if (notificationIntervalSeconds > 0 && currentIntervalMark > 0 && currentIntervalMark > lastNotifiedInterval) {
          const minutesPassed = currentIntervalMark * settings.notificationInterval;
          console.log(`Stopwatch: ${minutesPassed} minute(s) passed.`);
          // Use state variable for permission check
          if (notificationPermission === 'granted') {
            new Notification('Study Update', {
              body: `${minutesPassed} minute${minutesPassed > 1 ? 's' : ''} passed! Keep going!`,
              icon: '/favicon.ico', // Ensure this path is correct relative to the public folder
              tag: `stopwatch-interval-${currentIntervalMark}` // Use tag to prevent duplicate notifications
            });
          }
          setLastNotifiedInterval(currentIntervalMark); // Update the last notified interval
        }
      }
    }
  }, [timerStatus, startTime, accumulatedPausedTime, modeInternal, currentPhase, settings, handleTimerComplete, lastNotifiedInterval]); // Added lastNotifiedInterval dependency

  // --- Worker Setup and Communication ---
  useEffect(() => {
    // Initialize worker
    if (!timerWorkerRef.current) {
       try {
          timerWorkerRef.current = new Worker('/timerWorker.js');
          console.log("Worker initialized"); // Added log
       } catch (error) {
          console.error('Error initializing worker:', error);
          toast({
            title: "Timer Error",
            description: "Could not initialize the timer worker. Please refresh.",
            variant: "destructive"
          });
          return; // Exit if worker fails to initialize
       }
    }

    const worker = timerWorkerRef.current; // Use the ref

    // Define message handler using the latest calculateDisplayTime
    const handleWorkerMessage = (e: MessageEvent) => {
      timerWorkerRef.current = worker;

      if (e.data.type === 'TICK') {
        // console.log('Main: Received TICK');
        calculateDisplayTime(); // calculateDisplayTime is now a dependency of this effect
      }
    };

    // Define error handler
    const handleWorkerError = (error: ErrorEvent) => {
      console.error('Worker error:', error);
      toast({
        title: "Timer Error",
        description: "An unexpected error occurred with the timer worker.",
        variant: "destructive"
      });
      resetTimer(false); // Reset without saving
    };

    // Assign handlers
    worker.onmessage = handleWorkerMessage;
    worker.onerror = handleWorkerError;

    // If timer status changes to running, ensure worker is started
    // If timer status changes away from running, ensure worker is stopped
    if (timerStatus === 'running') {
        console.log("Effect: Timer is running, ensuring worker ticks START");
        worker.postMessage({ type: 'START' });
    } else {
        console.log("Effect: Timer is NOT running, ensuring worker ticks STOP");
        worker.postMessage({ type: 'STOP' });
    }

    // Cleanup function for this effect iteration
    return () => {
      // Remove the specific handlers assigned in this effect run
      // This prevents issues if the component re-renders quickly
      if (worker) {
          worker.onmessage = null;
          worker.onerror = null;
      }
      // Note: We don't terminate the worker here, only in the final unmount cleanup
    };
  }, [calculateDisplayTime, timerStatus]); // Re-run when calculation logic or status changes

  // Separate effect for final worker termination on unmount
  useEffect(() => {
    // Add beforeunload listener to save state as a fallback
    const handleBeforeUnload = () => {
        // Only save if timer is active (running or paused)
        if (timerStatus === 'running' || timerStatus === 'paused') {
            saveStateToSessionStorage();
        }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        // Save state one last time on unmount if active
        if (timerStatus === 'running' || timerStatus === 'paused') {
             saveStateToSessionStorage();
        }
        // Terminate worker
        if (timerWorkerRef.current) {
            console.log('Main: Terminating worker on unmount');
            timerWorkerRef.current.postMessage({ type: 'STOP' });
            timerWorkerRef.current.terminate();
            timerWorkerRef.current = null;
        }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timerStatus, saveStateToSessionStorage]); // Include timerStatus and save function


  // Effect for Initial State Restoration and Setting Initial Display Time
  useEffect(() => {
    const savedState = loadStateFromSessionStorage();

    if (savedState) {
      console.log("Restoring state from sessionStorage:", savedState);
      const timeElapsedSinceSave = Date.now() - savedState.saveTimestamp;

      // Restore non-time state directly
      setModeInternal(savedState.mode);
      setCurrentPhase(savedState.currentPhase);
      setCompletedSessions(savedState.completedSessions);
      // Handle subject restoration - we'll need to find the subject by name
      if (savedState.selectedSubject) {
        // We'll set this after loading subjects from Firebase
        // For now, just store the name to be used later
        const subjectToRestore = savedState.selectedSubject;
        // We'll use an effect to set this once subjects are loaded
      }

      let restoredStartTime = savedState.startTime;
      let restoredAccumulatedPausedTime = savedState.accumulatedPausedTime;
      let restoredStatus = savedState.status;
      let restoredPauseStartTime = savedState.pauseStartTime; // This is the timestamp when pause *began*

      // Adjust time based on state when saved
      if (savedState.status === 'running' && savedState.startTime) {
        // Timer was running. Treat the time since save as an implicit pause.
        // The accumulated time already includes pauses *before* saving.
        // Add the time elapsed *since* saving to the accumulated pause time.
        restoredAccumulatedPausedTime += timeElapsedSinceSave;
        // Keep the original startTime. Status remains 'running' for now.
      } else if (savedState.status === 'paused' && savedState.startTime && savedState.pauseStartTime) {
        // Timer was paused. The time since the pause *started* needs to be added.
        // savedState.accumulatedPausedTime has pauses *before* the final pause began.
        // Add the duration from when pause started until now.
        restoredAccumulatedPausedTime += (Date.now() - savedState.pauseStartTime);
        // Keep the original startTime. Status remains 'paused'.
      }

      // Set the restored state
      setStartTime(restoredStartTime); // Keep original start time
      setAccumulatedPausedTime(restoredAccumulatedPausedTime); // Set adjusted accumulated time
      setTimerStatus(restoredStatus); // Set status *after* time adjustments
      pauseStartTimeRef.current = restoredStatus === 'paused' ? restoredPauseStartTime : null; // Restore pause start timestamp if paused

      // Clear storage *after* successfully applying the state
      clearStateFromSessionStorage();

      // Trigger initial calculation based on restored state
      // Need to manually calculate initial display time based on restored values
      if (restoredStatus === 'running' && restoredStartTime !== null) {
         // Calculate active elapsed time: (now - start) - accumulated pauses
         const activeElapsedMs = Date.now() - restoredStartTime - restoredAccumulatedPausedTime;
         if (savedState.mode === 'pomodoro') {
             let phaseDurationSecs: number;
             switch (savedState.currentPhase) {
               case "work": phaseDurationSecs = settings.workDuration; break;
               case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
               case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
               default: phaseDurationSecs = settings.workDuration;
             }
             const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(activeElapsedMs / 1000));
             setDisplayTime(remainingSeconds);
             // Potentially handle immediate completion if remainingSeconds <= 0 after restore
             if (remainingSeconds <= 0 && savedState.mode === 'pomodoro') {
                // Decide how to handle this - maybe reset or trigger completion?
                // For now, just display 0. A manual start/reset might be needed.
                console.warn("Timer restored in running state but already completed.");
             }
         } else { // Stopwatch
             setDisplayTime(validateTime(Math.floor(activeElapsedMs / 1000)));
         }
      } else if (restoredStatus === 'paused' && restoredStartTime !== null && restoredPauseStartTime !== null) {
          // If paused, calculate active elapsed time up to the point it was paused
          // Active elapsed = (pause start time - session start time) - pauses before that pause
          const activeElapsedMs = restoredPauseStartTime - restoredStartTime - savedState.accumulatedPausedTime; // Use original accumulated time
           if (savedState.mode === 'pomodoro') {
               let phaseDurationSecs: number;
               switch (savedState.currentPhase) {
                 case "work": phaseDurationSecs = settings.workDuration; break;
                 case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
                 case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
                 default: phaseDurationSecs = settings.workDuration;
               }
               const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(activeElapsedMs / 1000));
               setDisplayTime(remainingSeconds);
           } else { // Stopwatch
               setDisplayTime(validateTime(Math.floor(activeElapsedMs / 1000)));
           }
      } else {
          // If idle or invalid state, set default display time (handled below)
      }


    } else {
        // No saved state, set initial display time based on props/defaults
        if (timerStatus === 'idle') { // Ensure this only runs if not restored above
            if (mode === 'pomodoro') {
                setDisplayTime(settings.workDuration);
                setCurrentPhase('work');
                setCompletedSessions(0);
            } else {
                setDisplayTime(0);
            }
        }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only on initial mount


  // Effect to check initial notification permission
  useEffect(() => {
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
    } else {
      console.warn("Browser does not support notifications.");
      setNotificationPermission("denied"); // Treat as denied if not supported
    }
  }, []);


  // Effect to save state when selected subject changes
  useEffect(() => {
    if (timerStatus !== 'idle') { // Only save if timer has been interacted with
        saveStateToSessionStorage();
    }
  }, [selectedSubject, saveStateToSessionStorage, timerStatus]);


  // Effect to set initial display time when settings change (if idle)
  useEffect(() => {
    if (timerStatus === 'idle') {
      if (modeInternal === 'pomodoro') {
        setDisplayTime(settings.workDuration);
        // Don't reset phase/sessions here, only on explicit reset or mode change
      } else {
        setDisplayTime(0);
      }
    }
  }, [settings, timerStatus, modeInternal]);


  // --- Picture-in-Picture Logic (Largely Unchanged, uses displayTime) ---

  const updatePipCanvas = useCallback(() => {
    const canvas = pipCanvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d', { alpha: false });
    if (!ctx) return;

    ctx.fillStyle = '#1a1f3c';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 48px monospace';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const timeToDisplay = validateTime(displayTime); // Use the unified displayTime state
    const hours = Math.floor(timeToDisplay / 3600);
    const minutes = Math.floor((timeToDisplay % 3600) / 60);
    const seconds = Math.floor(timeToDisplay % 60);
    let timeText = '';
    if (hours > 0) {
      timeText = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      timeText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    ctx.fillText(timeText, canvas.width / 2, canvas.height / 2);

    ctx.font = '16px sans-serif';
    ctx.fillText(selectedSubject?.name || 'No Subject', canvas.width / 2, 30);
    if (mode === "pomodoro") { // Use original prop 'mode' here for consistency? Or modeInternal? Check usage.
      ctx.fillStyle = '#a855f7';
      ctx.font = '14px sans-serif';
      ctx.fillText(currentPhase.charAt(0).toUpperCase() + currentPhase.slice(1), canvas.width / 2, canvas.height - 30);
    }
  }, [mode, displayTime, selectedSubject, currentPhase]); // Depend on displayTime

  useEffect(() => { // Setup PiP elements
    if (!isPipSupported) return;
    let stream: MediaStream | null = null;
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 320; canvas.height = 180;
      pipCanvasRef.current = canvas;
      const video = document.createElement('video');
      video.muted = true; video.playsInline = true; video.autoplay = true; video.loop = true;
      stream = canvas.captureStream ? canvas.captureStream(30) : (canvas as any).webkitCaptureStream ? (canvas as any).webkitCaptureStream() : null; // Added Safari fallback
      if (!stream) throw new Error("Canvas captureStream not supported");
      video.srcObject = stream;
      video.addEventListener('error', (e) => console.error('Video error:', e));
      video.addEventListener('enterpictureinpicture', () => { setIsPipActive(true); updatePipCanvas(); });
      video.addEventListener('leavepictureinpicture', () => setIsPipActive(false));
      video.play().catch((error) => { console.error('Video play error:', error); video.muted = true; return video.play(); });
      pipVideoRef.current = video;
      video.style.position = 'fixed'; video.style.opacity = '0'; video.style.pointerEvents = 'none'; video.style.zIndex = '-1';
      document.body.appendChild(video);
      updatePipCanvas();
      return () => {
        try {
          if (document.pictureInPictureElement === video) document.exitPictureInPicture().catch(console.error);
          video.srcObject = null;
          if (document.body.contains(video)) document.body.removeChild(video); // Check if attached before removing
          if (stream) stream.getTracks().forEach(track => track.stop());
          pipVideoRef.current = null; pipCanvasRef.current = null;
        } catch (error) { console.error('PiP Cleanup error:', error); }
      };
    } catch (error) {
      console.error('Error setting up PiP elements:', error); setIsPipSupported(false);
      if (stream) stream.getTracks().forEach(track => track.stop());
      return () => {};
    }
  }, [isPipSupported]); // Removed updatePipCanvas dependency

  useEffect(() => { // Update PiP canvas content
    if (!isPipActive || !pipCanvasRef.current || !pipVideoRef.current) return;
    if (pipVideoRef.current.paused) pipVideoRef.current.play().catch(console.error);
    updatePipCanvas();
  }, [isPipActive, displayTime, selectedSubject, currentPhase, updatePipCanvas]); // Depend on displayTime

  const togglePictureInPicture = async () => { // Toggle PiP
    try {
      if (!pipVideoRef.current) return;
      if (document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      } else {
        if (pipVideoRef.current.paused) await pipVideoRef.current.play();
        await pipVideoRef.current.requestPictureInPicture();
        updatePipCanvas();
      }
    } catch (error) {
      console.error('Error toggling PiP:', error);
      toast({ title: "PiP Error", description: `Could not toggle PiP: ${error instanceof Error ? error.message : 'Unknown error'}`, variant: "destructive" });
    }
  };

  useEffect(() => { // Check PiP Support
    const checkPipSupport = () => {
      try {
        const standardPipSupported = document.pictureInPictureEnabled;
        // @ts-ignore - Safari support
        const safariPipSupported = document.webkitPictureInPictureEnabled;
        const testVideo = document.createElement('video');
        // @ts-ignore - Safari specific API
        const safariPresentationSupported = testVideo.webkitSupportsPresentationMode && typeof testVideo.webkitSetPresentationMode === 'function';
        setIsPipSupported(!!(standardPipSupported || safariPipSupported || safariPresentationSupported));
      } catch (error) { console.error('Error checking PiP support:', error); setIsPipSupported(false); }
    };
    checkPipSupport();
  }, []);

  // Handle subject change
  const handleSubjectChange = (subject: Subject | null) => {
    setSelectedSubject(subject);
    // Save state when subject changes
    if (timerStatus !== 'idle') {
      saveStateToSessionStorage();
    }
  };


  // --- Notification Permission Request ---
  const requestNotificationPermission = async (): Promise<NotificationPermissionStatus> => {
    if (!('Notification' in window)) {
      toast({ title: "Notifications Not Supported", description: "Your browser does not support desktop notifications.", variant: "destructive" });
      setNotificationPermission("denied");
      return "denied";
    }

    // Return current status if already decided
    if (notificationPermission === 'granted' || notificationPermission === 'denied') {
      return notificationPermission;
    }

    // Request permission
    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission); // Update state
      if (permission === 'denied') {
        // Use 'default' variant or remove variant prop if 'default' is standard
        toast({ title: "Notifications Disabled", description: "You have denied notification permissions. Please enable them in browser settings if you want timer alerts.", variant: "default", duration: 5000 });
      } else if (permission === 'granted') {
         // Use 'default' variant or remove variant prop if 'default' is standard
         toast({ title: "Notifications Enabled", description: "You will now receive timer alerts.", variant: "default" });
      }
      return permission;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      toast({ title: "Permission Error", description: "Could not request notification permission.", variant: "destructive" });
      setNotificationPermission("denied"); // Assume denied on error
      return "denied";
    }
  };


  // --- Timer Control Functions (Rewritten) ---

  const startTimer = async () => { // Make async
    if (!selectedSubject || !selectedSubject.name) {
      toast({ title: "Select a subject", description: "Please select a subject first.", variant: "destructive" });
      return;
    }

    // Request permission if it's 'default' *before* proceeding
    if (notificationPermission === 'default') {
      const permissionResult = await requestNotificationPermission();
      // Optional: Could add logic here to prevent starting if permission is denied,
      // but current approach allows timer without notifications.
      // if (permissionResult === 'denied') {
      //   return; // Or show another toast
      // }
    }

    const now = Date.now();

    if (timerStatus === "idle") {
      // Starting fresh
      setStartTime(now);
      setAccumulatedPausedTime(0); // Reset accumulated pause time
      pauseStartTimeRef.current = null; // Ensure pause ref is clear
      sessionStartTimeRef.current = now; // Mark the beginning of this session segment

      if (modeInternal === 'pomodoro') {
         setDisplayTime(settings.workDuration); // Set initial display
         setCurrentPhase('work');
         setCompletedSessions(0);
      } else {
         setDisplayTime(0); // Stopwatch starts at 0
      }
      setTimerStatus("running");

    } else if (timerStatus === "paused") {
      // Resuming from pause
      if (pauseStartTimeRef.current !== null) {
        const pauseDurationMs = now - pauseStartTimeRef.current;
        setAccumulatedPausedTime(prev => prev + pauseDurationMs); // Add this pause duration
      }
      pauseStartTimeRef.current = null; // Clear pause start time
      // DO NOT reset startTime - keep the original session start time
      setTimerStatus("running");
    }

    // Save state *after* updating status
    saveStateToSessionStorage();

    if (timerWorkerRef.current) {
      timerWorkerRef.current.postMessage({ type: 'START' });
    }

    // Trigger calculation immediately after state update (effect will also run)
    // calculateDisplayTime(); // This might be redundant due to the effect
  };

  const pauseTimer = () => {
    if (timerStatus !== "running") return;

    if (timerWorkerRef.current) {
      // console.log('Main: Sending STOP to worker');
      timerWorkerRef.current.postMessage({ type: 'STOP' });
    }

    setTimerStatus("paused");
    const pauseTime = Date.now(); // Keep only one declaration
    pauseStartTimeRef.current = pauseTime; // Record when pause started

    // Save incomplete session immediately on pause (if it was running and is a work phase/stopwatch)
    // Calculate active duration up to the point of pause
    if (startTime && sessionStartTimeRef.current && (modeInternal === 'stopwatch' || currentPhase === "work")) {
        // Active elapsed = (pause time - session start time) - accumulated pauses *before* this one
        const activeElapsedMs = pauseTime - startTime - accumulatedPausedTime;
        if (activeElapsedMs > 0) { // Only save if there's actual duration
           saveStudySession(activeElapsedMs / 1000, false);
        }
    }

    // Save state *after* setting status and pause time
    saveStateToSessionStorage();
  };

  const resetTimer = async (shouldSave: boolean = true) => {
    if (timerWorkerRef.current) {
      // console.log('Main: Sending STOP to worker');
      timerWorkerRef.current.postMessage({ type: 'STOP' });
    }

    // Save final session state if running/paused and shouldSave is true
    if (shouldSave && timerStatus !== "idle" && sessionStartTimeRef.current && startTime) {
      let finalActiveDurationSecs = 0;
      const now = Date.now();

      if (timerStatus === 'running') {
        // Active duration = (now - start) - accumulated pauses
        finalActiveDurationSecs = (now - startTime - accumulatedPausedTime) / 1000;
      } else if (timerStatus === 'paused' && pauseStartTimeRef.current) {
        // Active duration = (pause start time - start time) - accumulated pauses
        finalActiveDurationSecs = (pauseStartTimeRef.current - startTime - accumulatedPausedTime) / 1000;
      }

      if (finalActiveDurationSecs > 0) {
        await saveStudySession(finalActiveDurationSecs, false); // Save as incomplete
      }
    }

    // Reset state
    setTimerStatus("idle"); // Set status first
    setStartTime(null);
    setAccumulatedPausedTime(0); // Reset accumulated time
    pauseStartTimeRef.current = null; // Clear pause start ref
    sessionStartTimeRef.current = null; // Clear session start ref
    setCurrentPhase("work"); // Reset phase
    setCompletedSessions(0); // Reset sessions
    setLastNotifiedInterval(0); // Reset notified interval on timer reset

    // Reset display time based on mode
    if (modeInternal === "pomodoro") {
      setDisplayTime(settings.workDuration);
    } else {
      setDisplayTime(0);
    }

    // Clear persisted state
    clearStateFromSessionStorage();
  };


  // --- Settings Handling (Unchanged logic, but ensure save) ---
  const handleSettingsOpen = () => {
    setTempSettings(settings);
    setIsSettingsOpen(true);
  };

  const handleSettingsSave = () => {
    setSettings(tempSettings); // This triggers the useLocalStorage hook to save
    setIsSettingsOpen(false);
    // If idle, update display time immediately based on new settings
    if (timerStatus === "idle" && modeInternal === "pomodoro") {
      const duration = currentPhase === "work"
        ? tempSettings.workDuration
        : currentPhase === "shortBreak"
        ? tempSettings.shortBreakDuration
        : tempSettings.longBreakDuration;
      setDisplayTime(duration);
      // Save state if settings change while idle? Optional.
      // saveStateToSessionStorage();
    }
  };

  // --- UI Rendering (Adapted for new state) ---
  const getPhaseEmoji = () => {
    switch (currentPhase) {
      case "work": return "📖";
      case "shortBreak": return "☕";
      case "longBreak": return "🌟";
      default: return "⏱️";
    }
  };

  // Removed workerError display block as error handling is now via toast/console

  return (
    <div className="flex flex-col items-center gap-10 w-full max-w-xl">
      {/* Subject Selection and Settings UI */}
      <div className="w-full flex justify-center">
        <div className="flex flex-col items-center gap-4">
          {/* Main controls row that becomes column on mobile */}
          <div className="flex flex-col sm:flex-row items-center gap-4">
            <div className="flex items-center gap-4 w-fit">
              <span role="img" aria-label="task" className="text-3xl">
                {modeInternal === "pomodoro" ? getPhaseEmoji() : "⏱️"}
              </span>
              <div className="flex-1">
                <SubjectManager
                  selectedSubject={selectedSubject}
                  onSubjectChange={handleSubjectChange}
                />
              </div>
            </div>
            {/* Controls that move below on mobile */}
            <div className="flex items-center gap-2 mt-2 sm:mt-0">
            <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent" onClick={handleSettingsOpen}>
                  <Settings className="h-5 w-5" />
                </Button>
              </DialogTrigger>
              {/* Theme-aware Settings Dialog Content */}
              <DialogContent className="bg-background dark:bg-[#1a1f3c]/90 backdrop-blur-md border border-border dark:border-white/10 text-foreground dark:text-white">
                <DialogHeader>
                  <DialogTitle>
                    {modeInternal === "pomodoro" ? "Pomodoro Settings" : "Stopwatch Settings"}
                  </DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  {/* Pomodoro-specific settings */}
                  {modeInternal === "pomodoro" && (
                    <>
                      <div className="grid gap-2">
                        <Label htmlFor="workDuration">Work Duration (minutes)</Label>
                        {/* Theme-aware Input */}
                        <Input id="workDuration" type="number" min="1" value={Math.floor(tempSettings.workDuration / 60)} onChange={(e) => { const minutes = parseInt(e.target.value); if (!isNaN(minutes) && minutes > 0) setTempSettings({ ...tempSettings, workDuration: minutes * 60 }); }} className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white" />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="shortBreak">Short Break (minutes)</Label>
                        {/* Theme-aware Input */}
                        <Input id="shortBreak" type="number" min="1" value={Math.floor(tempSettings.shortBreakDuration / 60)} onChange={(e) => { const minutes = parseInt(e.target.value); if (!isNaN(minutes) && minutes > 0) setTempSettings({ ...tempSettings, shortBreakDuration: minutes * 60 }); }} className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white" />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="longBreak">Long Break (minutes)</Label>
                        {/* Theme-aware Input */}
                        <Input id="longBreak" type="number" min="1" value={Math.floor(tempSettings.longBreakDuration / 60)} onChange={(e) => { const minutes = parseInt(e.target.value); if (!isNaN(minutes) && minutes > 0) setTempSettings({ ...tempSettings, longBreakDuration: minutes * 60 }); }} className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white" />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="sessions">Sessions until Long Break</Label>
                        {/* Theme-aware Input */}
                        <Input id="sessions" type="number" min="1" value={tempSettings.sessionsUntilLongBreak} onChange={(e) => { const sessions = parseInt(e.target.value); if (!isNaN(sessions) && sessions > 0) setTempSettings({ ...tempSettings, sessionsUntilLongBreak: sessions }); }} className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white" />
                      </div>
                    </>
                  )}
                  {/* Settings for both modes */}
                  <div className="grid gap-2">
                    <Label htmlFor="notificationInterval">Notification Interval (minutes)</Label>
                    {/* Theme-aware Input */}
                    <Input id="notificationInterval" type="number" min="1" value={tempSettings.notificationInterval} onChange={(e) => { const interval = parseInt(e.target.value); if (!isNaN(interval) && interval > 0) setTempSettings({ ...tempSettings, notificationInterval: interval }); }} className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white" />
                  </div>
                </div>
                <div className="flex justify-end gap-4">
                  {/* Theme-aware Reset Button */}
                  <Button variant="outline" onClick={() => setTempSettings(DEFAULT_SETTINGS)} className="text-foreground dark:text-white border-border dark:border-white/10 hover:bg-accent dark:hover:bg-white/5">Reset to Default</Button>
                  {/* Primary Save Button (styles likely ok) */}
                  <Button onClick={handleSettingsSave} className="bg-purple-600 hover:bg-purple-700 text-white">Save Changes</Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* Notification Permission Button */}
            {('Notification' in window) && ( // Only show if Notifications are potentially supported
              <Button
                variant="ghost"
                size="icon"
                className={`text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent ${notificationPermission === 'denied' ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={requestNotificationPermission}
                title={
                  notificationPermission === 'granted' ? "Notifications Enabled" :
                  notificationPermission === 'denied' ? "Notifications Disabled (Click to request again, may require browser settings change)" :
                  "Click to Enable Notifications"
                }
                disabled={notificationPermission === 'denied'} // Optionally disable if denied, though clicking might still be useful to trigger toast
              >
                {notificationPermission === 'granted' ? <BellRing className="h-5 w-5 text-green-500" /> : <BellOff className="h-5 w-5" />}
              </Button>
            )}

            {/* PiP Button */}
            {isPipSupported && (
              // Theme-aware PiP Button
              <Button variant="ghost" size="icon" className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent" onClick={togglePictureInPicture} title={isPipActive ? "Exit PiP" : "Enter PiP"}>
                {isPipActive ? <PictureInPicture2 className="h-5 w-5" /> : <PictureInPicture className="h-5 w-5" />}
              </Button>
            )}
          </div>
        </div>

        {/* Pomodoro Phase Display (Use internal mode) */}
        {modeInternal === "pomodoro" && (
          // Theme-aware text
          <div className="flex items-center gap-2 text-muted-foreground dark:text-white/60 text-sm">
            <span>Session {completedSessions + 1}</span>
            <span>•</span>
            <span className="capitalize">{currentPhase.replace(/([A-Z])/g, ' $1').trim()}</span>
          </div>
        )}
      </div>

      {/* Timer Display (Use internal mode) */}
      <TimerDisplay
        mode={modeInternal}
        timeRemaining={modeInternal === "pomodoro" ? displayTime : 0}
        elapsedTime={modeInternal === "stopwatch" ? displayTime : 0}
      />

      {/* Task Type and Description Fields */}
      <div className="w-full flex flex-col gap-4 my-6">
        <div className="grid gap-2">
          <Label htmlFor="taskType">Task Type</Label>
          <Select value={taskType} onValueChange={setTaskType}>
            <SelectTrigger className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent className="bg-background dark:bg-[#1a1f3c] border-border dark:border-white/10 text-foreground dark:text-white">
              <SelectItem value="Lecture">Lecture</SelectItem>
              <SelectItem value="Exercise">Exercise</SelectItem>
              <SelectItem value="Reading">Reading</SelectItem>
              <SelectItem value="Practice">Practice</SelectItem>
              <SelectItem value="Review">Review</SelectItem>
              <SelectItem value="Study">General Study</SelectItem>
              <SelectItem value="Custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="taskDescription">Description</Label>
          <Input
            id="taskDescription"
            placeholder={`What ${taskType.toLowerCase()} are you working on?`}
            value={taskDescription}
            onChange={(e) => setTaskDescription(e.target.value)}
            className="bg-background dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white"
          />
        </div>
      </div>

      {/* Timer Controls (Use internal mode) */}
      <TimerControls
        timerState={timerStatus}
        mode={modeInternal}
        onStart={startTimer}
        onPause={pauseTimer}
        onReset={() => resetTimer(true)} // Pass true to save potentially incomplete session
        onComplete={modeInternal === "stopwatch" ? async () => { // Make async
          // Save final stopwatch time on manual complete/stop
          if (timerStatus !== 'idle' && sessionStartTimeRef.current && startTime) {
             let finalActiveDurationSecs = 0;
             const now = Date.now();
             if (timerStatus === 'running') {
                 finalActiveDurationSecs = (now - startTime - accumulatedPausedTime) / 1000;
             } else if (timerStatus === 'paused' && pauseStartTimeRef.current) {
                 finalActiveDurationSecs = (pauseStartTimeRef.current - startTime - accumulatedPausedTime) / 1000;
             }

             if (finalActiveDurationSecs > 0) {
                await saveStudySession(finalActiveDurationSecs, true); // Save as completed
                toast({ title: "Session completed!", description: `You studied ${selectedSubject?.name} for ${formatDuration(finalActiveDurationSecs)}` });
             }
          }
          await resetTimer(false); // Reset state without saving again (already saved above)
        } : undefined}
      />
    </div>
  );
}
