import React, { useState, useEffect, useCallback } from 'react';
import { Comment } from '../types/chat';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Input } from './ui/input';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useToast } from './ui/use-toast';
import { User, Send, X, Trash2, CornerDownRight, ChevronRight, ChevronDown, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from './ui/avatar';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface CommentProps {
  comment: Comment;
  onReply: (parentId: string, content: string) => void;
  parentCommentId?: string;
  isNested?: boolean;
  expandedReplyIds: Set<string>;
  onExpandReply: (commentId: string) => void;
  onCollapseReply: (commentId: string) => void;
  isVisible?: boolean;
  isDeveloperMode?: boolean;
  handleDeleteReply?: (commentId: string, replyId: string, parentReplyId?: string) => void;
  handleDeleteComment?: (commentId: string) => void;
  handleEditComment?: (commentId: string, content: string) => void;
  handleEditReply?: (commentId: string, replyId: string, content: string, parentReplyId?: string) => void;
}

interface ReplyComponentProps {
  reply: Comment;
  onReplyClick: (author: string) => void;
  isDeveloperMode?: boolean;
  handleDeleteReply?: (commentId: string, replyId: string, parentReplyId?: string) => void;
  handleEditReply?: (commentId: string, replyId: string, content: string, parentReplyId?: string) => void;
  commentId: string;
  parentReplyId?: string;
  onViewReplies?: () => void;
  hasReplies?: boolean;
  isReplyExpanded?: boolean;
}

// Function to identify user in Featurebase
const identifyFeaturebaseUser = (user: any) => {
  if (typeof window.Featurebase === 'function') {
    window.Featurebase(
      "identify",
      {
        organization: "isotopeai",
        email: user.email || undefined,
        name: user.displayName || undefined,
        userId: user.uid,
        profilePicture: user.photoURL || undefined,
      },
      (err) => {
        if (err) {
          console.error("Featurebase identification error:", err);
        }
      }
    );
  }
};

// Function to handle Featurebase link clicks
const handleFeaturebaseLink = (href: string, user: any) => {
  if (href.includes('featurebase.app')) {
    // Identify user in Featurebase before opening the link
    identifyFeaturebaseUser(user);
  }
  window.open(href, '_blank', 'noopener,noreferrer');
};

const ReplyComponent: React.FC<ReplyComponentProps> = ({
  reply,
  onReplyClick,
  isDeveloperMode,
  handleDeleteReply,
  handleEditReply,
  commentId,
  parentReplyId,
  onViewReplies,
  hasReplies,
  isReplyExpanded
}) => {
  const { user } = useSupabaseAuth();
  // Use embedded photoURL directly from the reply prop
  const authorPhotoURL = reply.authorPhotoURL || ''; 
  const authorUsername = reply.authorUsername || reply.author || 'Anonymous'; // Use embedded username, fallback to author
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(reply.content);
  const { toast } = useToast();

  // Removed fetchAuthorProfile and related useEffect

  const handleSubmitEdit = () => {
    if (!editContent.trim()) {
      toast({
        title: "Empty Reply",
        description: "Please enter content before saving.",
        variant: "destructive",
      });
      return;
    }

    if (handleEditReply) {
      handleEditReply(commentId, reply.id, editContent.trim(), parentReplyId);
      setIsEditing(false);
    }
  };

  return (
    <div className="relative">
      <div className="flex gap-3 items-start">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <Avatar className="h-6 w-6">
              {/* Use authorPhotoURL directly */}
              <AvatarImage src={authorPhotoURL} alt={authorUsername} /> 
              <AvatarFallback className="bg-primary/10 text-primary/80 text-xs font-medium">
                {/* Use authorUsername for fallback */}
                {authorUsername.slice(0, 2).toUpperCase()} 
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col sm:flex-row sm:items-center gap-1">
              {/* Use authorUsername */}
              <span className="font-medium text-xs sm:text-sm">{authorUsername}</span> 
              <span className="text-xs text-muted-foreground">
                {new Date(reply.timestamp).toLocaleString()}
              </span>
            </div>
          </div>
          
          {isEditing ? (
            <div className="pl-8">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[60px] text-sm resize-none bg-background/50 focus:bg-background transition-colors duration-200"
              />
              <div className="flex items-center justify-end gap-2 mt-2">
                <Button 
                  size="sm" 
                  onClick={handleSubmitEdit}
                  className="text-xs py-1 h-auto gap-1 hover:scale-105 transition-all duration-200"
                >
                  <Send className="h-3 w-3" />
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setIsEditing(false);
                    setEditContent(reply.content);
                  }}
                  className="text-xs py-1 h-auto gap-1"
                >
                  <X className="h-3 w-3" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="pl-8">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                className="prose prose-sm max-w-none prose-a:text-primary prose-a:no-underline hover:prose-a:underline whitespace-pre-wrap text-sm math-display"
              >
                {reply.content}
              </ReactMarkdown>
            </div>
          )}
        </div>
        
        {isDeveloperMode && !isEditing && (
          <div className="flex-shrink-0 flex gap-1">
            <Button
              variant="outline"
              size="sm"
              className="h-7 w-7"
              onClick={() => setIsEditing(true)}
            >
              <Edit className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="destructive"
              size="sm"
              className="h-7 w-7"
              onClick={() => handleDeleteReply && handleDeleteReply(commentId, reply.id, parentReplyId)}
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </div>
        )}
      </div>
      <div className="flex items-center gap-2 pl-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onReplyClick(reply.author)}
          className="text-xs sm:text-sm py-1 h-auto hover:bg-primary/10 transition-colors duration-200"
        >
          Reply
        </Button>
        
        {hasReplies && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onViewReplies}
            className="text-xs sm:text-sm py-1 h-auto hover:bg-primary/10 transition-colors duration-200 gap-1"
          >
            {isReplyExpanded ? (
              <>
                <ChevronDown className="h-3 w-3" />
                Hide Replies
              </>
            ) : (
              <>
                <ChevronRight className="h-3 w-3" />
                View {reply.replies?.length} {reply.replies?.length === 1 ? 'Reply' : 'Replies'}
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export const CommentComponent: React.FC<CommentProps> = ({ 
  comment, 
  onReply, 
  parentCommentId,
  isNested = false,
  expandedReplyIds,
  onExpandReply,
  onCollapseReply,
  isVisible = true,
  isDeveloperMode = false,
  handleDeleteReply,
  handleDeleteComment,
  handleEditComment,
  handleEditReply
}) => {
  const { user } = useSupabaseAuth();
  const [isReplying, setIsReplying] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [username, setUsername] = useState<string>("");
  // Use embedded photoURL directly from the comment prop
  const authorPhotoURL = comment.authorPhotoURL || ''; 
  const authorUsername = comment.authorUsername || comment.author || 'Anonymous'; // Use embedded username, fallback to author
  const [expandedReplyThreads, setExpandedReplyThreads] = useState<Set<string>>(new Set());
  const [expandedNestedReplyThreads, setExpandedNestedReplyThreads] = useState<Set<string>>(new Set());
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const { toast } = useToast();

  // Removed fetchAuthorProfile

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      try {
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const userData = userSnap.data();
          setUsername(userData.username || "");
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Removed useEffect for fetchAuthorProfile

  const handleSubmitReply = () => {
    if (!replyContent.trim()) return;
    
    if (!username) {
      toast({
        title: "Error",
        description: "Please wait while we load your profile.",
        variant: "destructive",
      });
      return;
    }

    const replyId = parentCommentId ? `${parentCommentId}_${comment.id}` : comment.id;
    onReply(replyId, replyContent);
    setReplyContent('');
    setIsReplying(false);
  };

  const handleSubmitEdit = () => {
    if (!editContent.trim()) {
      toast({
        title: "Empty Comment",
        description: "Please enter content before saving.",
        variant: "destructive",
      });
      return;
    }

    handleEditComment && handleEditComment(comment.id, editContent.trim());
    setIsEditing(false);
  };

  const currentCommentFullId = parentCommentId ? `${parentCommentId}_${comment.id}` : comment.id;
  const hasReplies = comment.replies && comment.replies.length > 0;
  const isExpanded = expandedReplyIds.has(comment.id);

  const handleToggleReplies = () => {
    if (isExpanded) {
      onCollapseReply(comment.id);
      // When collapsing comment replies, also collapse all reply threads
      setExpandedReplyThreads(new Set());
      setExpandedNestedReplyThreads(new Set());
    } else {
      onExpandReply(comment.id);
    }
  };

  const handleToggleReplyThread = (replyId: string) => {
    setExpandedReplyThreads(prev => {
      const newSet = new Set(prev);
      if (newSet.has(replyId)) {
        newSet.delete(replyId);
        // Also collapse all nested reply threads under this reply
        setExpandedNestedReplyThreads(prev => {
          const nestedSet = new Set(prev);
          [...nestedSet].forEach(id => {
            if (id.startsWith(`${replyId}_`)) {
              nestedSet.delete(id);
            }
          });
          return nestedSet;
        });
      } else {
        newSet.add(replyId);
      }
      return newSet;
    });
  };

  const handleToggleNestedReplyThread = (nestedReplyId: string) => {
    setExpandedNestedReplyThreads(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nestedReplyId)) {
        newSet.delete(nestedReplyId);
      } else {
        newSet.add(nestedReplyId);
      }
      return newSet;
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={cn(
      "relative group",
      isNested ? 'ml-6 sm:ml-8' : '',
      "animate-fade-in"
    )}>
      {isNested && (
        <div className="absolute left-0 top-0 bottom-0 w-px bg-border/50 -ml-3 sm:-ml-4 group-hover:bg-primary/30 transition-colors duration-200" />
      )}
      <div className="flex flex-col space-y-3 p-3 sm:p-4 rounded-lg bg-muted/20 hover:bg-muted/30 transition-all duration-200">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Avatar className="h-8 w-8 ring-1 ring-border/50 group-hover:ring-primary/20 transition-all duration-200">
                {/* Use authorPhotoURL directly */}
                <AvatarImage src={authorPhotoURL} alt={authorUsername} /> 
                <AvatarFallback className="bg-primary/10">
                  {/* Use authorUsername for fallback */}
                  {authorUsername.slice(0, 2).toUpperCase()} 
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                {/* Use authorUsername */}
                <span className="font-medium text-sm sm:text-base">{authorUsername}</span> 
                <span className="text-xs text-muted-foreground">
                  {new Date(comment.timestamp).toLocaleString()}
                </span>
              </div>
            </div>
            {isEditing ? (
              <div className="text-sm sm:text-base pl-10">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="min-h-[80px] sm:min-h-[100px] text-sm sm:text-base resize-none bg-background/50 focus:bg-background transition-colors duration-200"
                />
                <div className="flex items-center justify-end gap-2 mt-2">
                  <Button 
                    size="sm" 
                    onClick={handleSubmitEdit}
                    className="text-xs sm:text-sm py-1 h-auto gap-2 hover:scale-105 transition-all duration-200"
                  >
                    <Send className="h-3.5 w-3.5" />
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setIsEditing(false);
                      setEditContent(comment.content);
                    }}
                    className="text-xs sm:text-sm py-1 h-auto gap-2"
                  >
                    <X className="h-3.5 w-3.5" />
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-sm sm:text-base pl-10">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  className="prose prose-sm max-w-none prose-a:text-primary prose-a:no-underline hover:prose-a:underline whitespace-pre-wrap math-display"
                  components={{
                    a: ({ node, href, ...props }) => (
                      <a 
                        {...props} 
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        data-featurebase-link={href?.includes('featurebase.app') ? true : undefined}
                        className="text-primary hover:underline transition-all duration-200"
                      />
                    ),
                    p: ({ children }) => (
                      <p className="mb-2 last:mb-0">{children}</p>
                    ),
                  }}
                >
                  {comment.content}
                </ReactMarkdown>
              </div>
            )}
          </div>
          {isDeveloperMode && !isEditing && (
            <div className="flex-shrink-0 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="h-8 w-8"
                onClick={() => handleDeleteComment && handleDeleteComment(comment.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 pl-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsReplying(!isReplying)}
            className={cn(
              "text-xs sm:text-sm py-1 h-auto hover:bg-primary/10 transition-colors duration-200",
              isReplying && "text-primary"
            )}
          >
            Reply
          </Button>
          {hasReplies && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleReplies}
              className="text-xs sm:text-sm py-1 h-auto hover:bg-primary/10 transition-colors duration-200 gap-1"
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-3.5 w-3.5" />
                  Hide Replies
                </>
              ) : (
                <>
                  <ChevronRight className="h-3.5 w-3.5" />
                  Show {comment.replies?.length} {comment.replies?.length === 1 ? 'Reply' : 'Replies'}
                </>
              )}
            </Button>
          )}
        </div>

        <div className={cn(
          "overflow-hidden transition-all duration-200",
          isReplying ? "max-h-[300px] opacity-100" : "max-h-0 opacity-0"
        )}>
          <div className="space-y-3 pl-10">
            <Textarea
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              placeholder="Write a reply..."
              className="min-h-[80px] sm:min-h-[100px] text-sm sm:text-base resize-none bg-background/50 focus:bg-background transition-colors duration-200"
            />
            <div className="flex items-center justify-end gap-2">
              <Button 
                size="sm" 
                onClick={handleSubmitReply}
                disabled={!username || !replyContent.trim()}
                className="text-xs sm:text-sm py-1 h-auto gap-2 hover:scale-105 transition-all duration-200"
              >
                <Send className="h-3.5 w-3.5" />
                Submit
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setIsReplying(false);
                  setReplyContent('');
                }}
                className="text-xs sm:text-sm py-1 h-auto gap-2"
              >
                <X className="h-3.5 w-3.5" />
                Cancel
              </Button>
            </div>
          </div>
        </div>

        <div className={cn(
          "overflow-hidden transition-all duration-200",
          hasReplies && isExpanded ? "max-h-max opacity-100" : "max-h-0 opacity-0"
        )}>
          <div className="space-y-3 mt-2">
            {comment.replies?.sort((a, b) => b.timestamp - a.timestamp).map((reply) => (
              <div key={reply.id} className="ml-6 sm:ml-8 relative group">
                <div className="absolute left-0 top-0 bottom-0 w-px bg-border/50 -ml-3 sm:-ml-4 group-hover:bg-primary/30 transition-colors duration-200" />
                
                <ReplyComponent
                  reply={reply}
                  onReplyClick={(author) => {
                    setIsReplying(true);
                    setReplyContent(`@${author} `);
                  }}
                  isDeveloperMode={isDeveloperMode}
                  handleDeleteReply={handleDeleteReply}
                  handleEditReply={handleEditReply}
                  commentId={comment.id}
                  parentReplyId={reply.id}
                  hasReplies={reply.replies && reply.replies.length > 0}
                  onViewReplies={() => handleToggleReplyThread(reply.id)}
                  isReplyExpanded={expandedReplyThreads.has(reply.id)}
                />
                
                {/* Nested reply thread that can be expanded/collapsed */}
                <div className={cn(
                  "overflow-hidden transition-all duration-200 ml-6 space-y-2 mt-2",
                  reply.replies && reply.replies.length > 0 && expandedReplyThreads.has(reply.id) 
                    ? "max-h-max opacity-100" 
                    : "max-h-0 opacity-0"
                )}>
                  {reply.replies && reply.replies.length > 0 && expandedReplyThreads.has(reply.id) && (
                    <div className="relative">
                      <div className="absolute left-0 top-0 bottom-0 w-px bg-border/50 -ml-3 sm:-ml-4 group-hover:bg-primary/30 transition-colors duration-200" />
                      <div className="space-y-3">
                        {reply.replies.sort((a, b) => b.timestamp - a.timestamp).map((nestedReply) => (
                          <div key={nestedReply.id} className="relative">
                            <ReplyComponent
                              reply={nestedReply}
                              onReplyClick={(author) => {
                                setIsReplying(true);
                                setReplyContent(`@${author} `);
                              }}
                              isDeveloperMode={isDeveloperMode}
                              handleDeleteReply={handleDeleteReply}
                              handleEditReply={handleEditReply}
                              commentId={comment.id}
                              parentReplyId={reply.id}
                              hasReplies={nestedReply.replies && nestedReply.replies.length > 0}
                              onViewReplies={() => handleToggleNestedReplyThread(`${reply.id}_${nestedReply.id}`)}
                              isReplyExpanded={expandedNestedReplyThreads.has(`${reply.id}_${nestedReply.id}`)}
                            />
                            
                            {/* Deeply Nested reply thread */}
                            {nestedReply.replies && nestedReply.replies.length > 0 && (
                              <div className={cn(
                                "overflow-hidden transition-all duration-200 ml-6 space-y-2 mt-2",
                                expandedNestedReplyThreads.has(`${reply.id}_${nestedReply.id}`)
                                  ? "max-h-max opacity-100" 
                                  : "max-h-0 opacity-0"
                              )}>
                                {expandedNestedReplyThreads.has(`${reply.id}_${nestedReply.id}`) && (
                                  <div className="relative ml-6">
                                    <div className="absolute left-0 top-0 bottom-0 w-px bg-border/50 -ml-3 sm:-ml-4 group-hover:bg-primary/30 transition-colors duration-200" />
                                    <div className="space-y-3">
                                      {nestedReply.replies.sort((a, b) => b.timestamp - a.timestamp).map((deepNestedReply) => (
                                        <CommentComponent
                                          key={deepNestedReply.id}
                                          comment={deepNestedReply}
                                          onReply={onReply}
                                          parentCommentId={`${reply.id}_${nestedReply.id}`}
                                          isNested
                                          expandedReplyIds={expandedReplyIds}
                                          onExpandReply={onExpandReply}
                                          onCollapseReply={onCollapseReply}
                                          isDeveloperMode={isDeveloperMode}
                                          handleDeleteReply={handleDeleteReply}
                                          handleDeleteComment={handleDeleteComment}
                                          handleEditComment={handleEditComment}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
