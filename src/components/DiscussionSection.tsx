import React, { useState, useEffect } from 'react';
import { CommentComponent } from './Comment';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { ChevronDown, ChevronUp, MessageCircle, X, MessageSquare, MessageSquarePlus } from 'lucide-react';
import { useToast } from './ui/use-toast';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Updated Comment interface to match Supabase schema
export interface DiscussionComment {
  id: string;
  chat_id: string;
  author_id: string;
  author_name: string | null;
  author_username: string | null;
  author_photo_url: string | null;
  content: string;
  parent_id: string | null;
  created_at: string;
  updated_at: string;
  replies?: DiscussionComment[];
}

interface CommentsSectionProps {
  chatId: string;
}

// Supabase functions for discussion operations
const addDiscussionComment = async (chatId: string, content: string, authorId: string, authorName: string, authorUsername: string, authorPhotoUrl?: string, parentId?: string): Promise<DiscussionComment> => {
  // Use type assertion to bypass TypeScript issues with table not being in types
  const { data, error } = await (supabase as any)
    .from('discussions')
    .insert({
      chat_id: chatId,
      content: content,
      author_id: authorId,
      author_name: authorName,
      author_username: authorUsername,
      author_photo_url: authorPhotoUrl || null,
      parent_id: parentId || null
    })
    .select()
    .single();

  if (error) {
    console.error("Error adding discussion comment:", error);
    throw error;
  }

  return {
    ...data,
    replies: []
  };
};

const updateDiscussionComment = async (commentId: string, content: string): Promise<void> => {
  const { error } = await (supabase as any)
    .from('discussions')
    .update({
      content: content,
      updated_at: new Date().toISOString()
    })
    .eq('id', commentId);

  if (error) {
    console.error("Error updating discussion comment:", error);
    throw error;
  }
};

const deleteDiscussionComment = async (commentId: string): Promise<void> => {
  const { error } = await (supabase as any)
    .from('discussions')
    .delete()
    .eq('id', commentId);

  if (error) {
    console.error("Error deleting discussion comment:", error);
    throw error;
  }
};

const fetchCommentsWithReplies = async (chatId: string): Promise<DiscussionComment[]> => {
  try {
    console.log('Fetching comments for chatId:', chatId);

    // First, get all comments for this chat
    const { data: allComments, error } = await (supabase as any)
      .from('discussions')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true });

    console.log('Raw comments from Supabase:', { allComments, error });

    if (error) {
      console.error("Error fetching comments:", error);
      return [];
    }

    if (!allComments || allComments.length === 0) {
      console.log('No comments found for chatId:', chatId);
      return [];
    }

    console.log(`Found ${allComments.length} comments for chatId:`, chatId);

    // Organize comments into a hierarchical structure
    const commentsMap = new Map<string, DiscussionComment>();
    const rootComments: DiscussionComment[] = [];

    // First pass: create all comment objects
    allComments.forEach((comment: any) => {
      const discussionComment: DiscussionComment = {
        id: comment.id,
        chat_id: comment.chat_id,
        author_id: comment.author_id,
        author_name: comment.author_name,
        author_username: comment.author_username,
        author_photo_url: comment.author_photo_url,
        content: comment.content,
        parent_id: comment.parent_id,
        created_at: comment.created_at,
        updated_at: comment.updated_at,
        replies: []
      };
      commentsMap.set(comment.id, discussionComment);
    });

    // Second pass: organize into hierarchy
    allComments.forEach((comment: any) => {
      const discussionComment = commentsMap.get(comment.id)!;
      if (comment.parent_id) {
        const parent = commentsMap.get(comment.parent_id);
        if (parent) {
          parent.replies!.push(discussionComment);
        } else {
          console.warn('Parent comment not found for reply:', comment.id, 'parent:', comment.parent_id);
        }
      } else {
        rootComments.push(discussionComment);
      }
    });

    console.log('Organized comments:', { rootComments, totalComments: allComments.length });
    return rootComments;
  } catch (error) {
    console.error("Error in fetchCommentsWithReplies:", error);
    return [];
  }
};

const subscribeToComments = (chatId: string, callback: (comments: DiscussionComment[]) => void) => {
  const subscription = supabase
    .channel(`discussions:${chatId}`)
    .on(
      "postgres_changes",
      { event: "*", schema: "public", table: "discussions", filter: `chat_id=eq.${chatId}` },
      async (payload) => {
        // Fetch all comments for the chat after a change
        const comments = await fetchCommentsWithReplies(chatId);
        callback(comments);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

const COMMENTS_PER_GROUP = 5;

// Animation utilities for header elements
const animateIn = "animate-in fade-in slide-in-from-bottom-2 duration-500 fill-mode-both";
const staggerDelay = (index: number) => `delay-[${100 + (index * 75)}ms]`;

export const DiscussionSection: React.FC<CommentsSectionProps> = ({
  chatId,
}) => {
  const { user } = useSupabaseAuth();
  const [comments, setComments] = useState<DiscussionComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [maxVisibleGroup, setMaxVisibleGroup] = useState(0);
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [expandedReplyIds, setExpandedReplyIds] = useState<Set<string>>(new Set());
  const [username, setUsername] = useState<string>("");
  const [isDeveloper, setIsDeveloper] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const { toast } = useToast();

  // Debug: Log the chatId being received
  console.log('DiscussionSection received chatId:', chatId);

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      try {
        const { data, error } = await supabase
          .from('users')
          .select('username')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
          return;
        }

        setUsername(data?.username || "");
        setIsDeveloper(data?.username === "Developer");
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, [user]);

  useEffect(() => {
    if (!chatId) {
      toast({
        title: "Error",
        description: "No chat selected. Please start a new chat.",
        variant: "destructive",
      });
      return;
    }

    // Initial fetch of comments
    const loadInitialComments = async () => {
      console.log('Loading initial comments for chatId:', chatId);
      try {
        const initialComments = await fetchCommentsWithReplies(chatId);
        console.log('Initial comments loaded:', initialComments);
        setComments(initialComments);
      } catch (error) {
        console.error('Error loading initial comments:', error);
      }
    };

    loadInitialComments();

    // Set up real-time subscription
    const unsubscribe = subscribeToComments(chatId, (updatedComments) => {
      console.log('Real-time comments update:', updatedComments);
      setComments(updatedComments);
    });

    return () => {
      try {
        unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing from comments:", error);
      }
    };
  }, [chatId, toast]);

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast({
        title: "Empty Comment",
        description: "Please enter a comment before submitting.",
        variant: "destructive",
      });
      return;
    }

    if (!username || !user) {
      toast({
        title: "Error",
        description: "Please wait while we load your profile.",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Adding comment:', { chatId, content: newComment.trim(), userId: user.id, username });
      const result = await addDiscussionComment(chatId, newComment.trim(), user.id, username, username);
      console.log('Comment added successfully:', result);

      setNewComment('');
      setShowCommentInput(false);

      // Manually refresh comments to ensure UI updates
      const updatedComments = await fetchCommentsWithReplies(chatId);
      console.log('Refreshed comments after add:', updatedComments);
      setComments(updatedComments);

      toast({
        title: "Success",
        description: "Comment added successfully!",
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddReply = async (parentId: string, content: string) => {
    if (!username || !user) {
      toast({
        title: "Error",
        description: "Please wait while we load your profile.",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Adding reply:', { chatId, content, userId: user.id, username, parentId });
      const result = await addDiscussionComment(chatId, content, user.id, username, username, undefined, parentId);
      console.log('Reply added successfully:', result);

      // Manually refresh comments to ensure UI updates
      const updatedComments = await fetchCommentsWithReplies(chatId);
      console.log('Refreshed comments after reply:', updatedComments);
      setComments(updatedComments);

      toast({
        title: "Success",
        description: "Reply added successfully!",
      });
    } catch (error: any) {
      console.error('Error adding reply:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExpandReply = (commentId: string) => {
    setExpandedReplyIds(prev => new Set([...prev, commentId]));
  };

  const handleCollapseReply = (commentId: string) => {
    setExpandedReplyIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(commentId);
      return newSet;
    });
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteDiscussionComment(commentId);
      toast({
        title: "Success",
        description: "Comment deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReply = async (commentId: string, replyId: string, parentReplyId?: string) => {
    try {
      // For replies, we delete the reply comment directly
      await deleteDiscussionComment(replyId);
      toast({
        title: "Success",
        description: "Reply deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast({
        title: "Error",
        description: "Failed to delete reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditReply = async (_commentId: string, replyId: string, content: string, _parentReplyId?: string) => {
    try {
      // For replies, we edit the reply comment directly
      await updateDiscussionComment(replyId, content);
      toast({
        title: "Success",
        description: "Reply updated successfully.",
      });
    } catch (error) {
      console.error('Error updating reply:', error);
      toast({
        title: "Error",
        description: "Failed to update reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Convert DiscussionComment to Comment for CommentComponent compatibility
  const convertToComment = (discussionComment: DiscussionComment): any => {
    return {
      id: discussionComment.id,
      content: discussionComment.content,
      author: discussionComment.author_name || 'Anonymous',
      authorId: discussionComment.author_id,
      authorUsername: discussionComment.author_username,
      authorPhotoURL: discussionComment.author_photo_url,
      timestamp: new Date(discussionComment.created_at).getTime(),
      parentId: discussionComment.parent_id,
      replies: discussionComment.replies?.map(convertToComment) || []
    };
  };

  const handleEditComment = async (commentId: string, content: string) => {
    try {
      await updateDiscussionComment(commentId, content);

      // Reset editing state
      setEditingCommentId(null);
      setEditedContent('');

      toast({
        title: "Success",
        description: "Comment updated successfully.",
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const visibleComments = comments.slice(0, (maxVisibleGroup + 1) * COMMENTS_PER_GROUP);
  const hasMoreComments = comments.length > visibleComments.length;

  return (
    <div className="w-full bg-card rounded-xl sm:rounded-2xl shadow-lg p-3 sm:p-6 transition-all duration-300 hover:shadow-xl max-w-full">
      <div className="flex flex-col sm:flex-row items-start justify-between mb-4 sm:mb-8 gap-2">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className={`bg-primary/10 p-1.5 sm:p-2 rounded-full ${animateIn} ${staggerDelay(0)}`}>
            <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-primary" />
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <h2 className={`text-base sm:text-lg md:text-2xl font-semibold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent ${animateIn} ${staggerDelay(1)}`}>
              Discussion
            </h2>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <a
                    href="https://isotopeai.featurebase.app"
                    target="_blank"
                    rel="noopener noreferrer"
                    data-featurebase-link
                    className={`inline-flex items-center gap-1.5 text-xs sm:text-sm text-muted-foreground hover:text-primary transition-colors duration-200 bg-muted/50 hover:bg-muted px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full ${animateIn} ${staggerDelay(2)}`}
                  >
                    <MessageSquarePlus className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    <span className="hidden sm:inline">Request Features, Report Bugs, Give Feedback</span>
                    <span className="sm:hidden">Feedback</span>
                  </a>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Submit feature requests, report bugs, or give feedback</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className={`flex items-center gap-3 sm:gap-4 self-end ${animateIn} ${staggerDelay(3)}`}>
          {username && (
            <span className="text-xs sm:text-sm text-muted-foreground bg-muted/50 px-3 py-1 rounded-full">
              Signed in as {username}
            </span>
          )}
          {/* Debug button to manually fetch comments */}
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              console.log('Manual fetch triggered for chatId:', chatId);
              const testComments = await fetchCommentsWithReplies(chatId);
              console.log('Manual fetch result:', testComments);
              setComments(testComments);
            }}
            className="text-xs"
          >
            Refresh
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowCommentInput(!showCommentInput)}
            className={cn(
              "rounded-full hover:bg-primary/10 transition-all duration-300",
              showCommentInput && "rotate-90"
            )}
          >
            {showCommentInput ? (
              <X className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
            ) : (
              <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
            )}
          </Button>
        </div>
      </div>

      <div className={cn(
        "overflow-hidden transition-all duration-300",
        showCommentInput ? "max-h-[300px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="mb-4 sm:mb-6 space-y-2 sm:space-y-3 md:space-y-4 bg-white/5 p-3 sm:p-4 md:p-5 rounded-lg border border-border/50 backdrop-blur-sm">
          <div className="flex flex-col space-y-2 sm:space-y-3">
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Share your thoughts..."
              className="min-h-[80px] sm:min-h-[100px] md:min-h-[120px] text-xs sm:text-sm md:text-base resize-none bg-background/50 focus:bg-background transition-colors duration-200"
            />
            <Button
              onClick={handleSubmitComment}
              className="self-end text-xs sm:text-sm md:text-base px-3 sm:px-4 md:px-6 py-1 sm:py-2 transition-all duration-200 hover:scale-105"
              disabled={!username}
            >
              Post Comment
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {visibleComments.map((comment) => (
          <CommentComponent
            key={comment.id}
            comment={convertToComment(comment)}
            onReply={handleAddReply}
            expandedReplyIds={expandedReplyIds}
            onExpandReply={handleExpandReply}
            onCollapseReply={handleCollapseReply}
            isDeveloperMode={isDeveloper || localStorage.getItem("isDeveloperMode") === "true"}
            handleDeleteReply={handleDeleteReply}
            handleDeleteComment={handleDeleteComment}
            handleEditComment={handleEditComment}
            handleEditReply={handleEditReply}
          />
        ))}
        {comments.length === 0 && (
          <div className={`text-left py-6 sm:py-8 md:py-12 px-3 sm:px-4 bg-muted/30 rounded-lg border border-border/50 ${animateIn} ${staggerDelay(4)}`}>
            <div className="flex items-center gap-2 sm:gap-3">
              <MessageSquare className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 text-muted-foreground/50" />
              <p className="text-xs sm:text-sm md:text-base text-muted-foreground">
                No discussions yet. Be the first to start a discussion!
                <br />
                <small className="text-muted-foreground/70">
                  Debug: ChatId = {chatId}, Comments count = {comments.length}
                </small>
              </p>
            </div>
          </div>
        )}
      </div>

      {hasMoreComments && (
        <Button
          variant="ghost"
          className={`mt-6 w-full flex items-center justify-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 ${animateIn} ${staggerDelay(5)}`}
          onClick={() => setMaxVisibleGroup(prev => prev + 1)}
        >
          <ChevronDown className="h-4 w-4" />
          Show More Comments
        </Button>
      )}

      {maxVisibleGroup > 0 && (
        <Button
          variant="ghost"
          className={`mt-2 w-full flex items-center justify-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 ${animateIn} ${staggerDelay(6)}`}
          onClick={() => setMaxVisibleGroup(0)}
        >
          <ChevronUp className="h-4 w-4" />
          Show Less
        </Button>
      )}
    </div>
  );
};
